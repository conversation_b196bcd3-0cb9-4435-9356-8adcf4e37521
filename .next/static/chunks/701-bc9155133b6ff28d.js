"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[701],{427:(e,t,s)=>{s.d(t,{D:()=>l,t:()=>i});var a=s(4241);let r="pk_test_51Rbz5DG3dq2cZKhWQAhsMvCKvlJ7qki27vhezUpnbMECmp53uG6V3JmEy7pQodTCBWYNKeP55X64ZIJxaXfkijrM00xhD0btGs";r||console.warn("Missing NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY environment variable");let i=r?(0,a.c)(r):null,l={starter:{name:"Starter",price:10,priceId:"price_1Rbz9CG3dq2cZKhWCnIVHUx6",features:["50 credits (~50 subtitle downloads)","VTT and TXT format downloads","Manual and Auto-generated caption support","English subtitle extraction","Multi-language support","Credits valid for 6 months"],limits:{creditsPerMonth:50,creditsPerAction:1,actionsPerExtraction:1},popular:!1},pro:{name:"Pro",price:30,priceId:"price_1RbzCCG3dq2cZKhWS3MlXiCZ",features:["200 credits (~200 subtitle downloads)","VTT and TXT format downloads","Manual and Auto-generated caption support","English subtitle extraction","Multi-language support","Credits valid for 6 months"],limits:{creditsPerMonth:200,creditsPerAction:1,actionsPerExtraction:1},popular:!0},creator:{name:"Creator",price:75,priceId:"price_1RbzDHG3dq2cZKhWID6C0aMY",features:["600 credits (~600 subtitle downloads)","VTT and TXT format downloads","Manual and Auto-generated caption support","English subtitle extraction","Multi-language support","Credits valid for 6 months"],limits:{creditsPerMonth:600,creditsPerAction:1,actionsPerExtraction:1},popular:!1}}},3215:(e,t,s)=>{s.d(t,{A:()=>o});var a=s(7876);s(4232);var r=s(6960),i=s(9969),l=s(7446),n=s(3374);let o=e=>{let{variant:t="default",size:s="default",className:o="",children:c}=e,{signInWithGoogle:d,loading:m}=(0,i.A)();return(0,a.jsxs)(r.$,{onClick:d,disabled:m,variant:t,size:s,className:o,children:[m?(0,a.jsx)(l.A,{className:"w-4 h-4 mr-2 animate-spin"}):(0,a.jsx)(n.A,{className:"w-4 h-4 mr-2"}),c||"Sign in with Google"]})}},4518:(e,t,s)=>{s.d(t,{A:()=>d});var a=s(7876),r=s(5259),i=s(1886),l=s(2347),n=s(760),o=s(7297),c=s(5390);let d=e=>{let{onTermsClick:t,onPrivacyClick:s,onDisclaimerClick:d}=e,m=new Date().getFullYear(),x={product:[{name:"Features",href:"#features"},{name:"How to Use",href:"#how-to-use"},{name:"Use Cases",href:"#use-cases"},{name:"FAQ",href:"#faq"}],legal:[{name:"Privacy Policy",onClick:s},{name:"Terms of Service",onClick:t},{name:"Disclaimer",onClick:d}],social:[{name:"GitHub",href:"#",icon:(0,a.jsx)(r.ERf,{className:"w-5 h-5"})},{name:"Twitter",href:"#",icon:(0,a.jsx)(r.gSO,{className:"w-5 h-5"})},{name:"Email",href:"mailto:<EMAIL>",icon:(0,a.jsx)(l.A,{className:"w-5 h-5"})}]};return(0,a.jsx)("footer",{className:"bg-slate-900 border-t border-slate-800",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-3 sm:px-6 lg:px-8 py-8 sm:py-12",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8",children:[(0,a.jsxs)(i.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"col-span-1 sm:col-span-2",children:[(0,a.jsxs)("h3",{className:"text-xl sm:text-2xl font-bold text-white mb-3 sm:mb-4",children:["YouTube Subtitle",(0,a.jsxs)("span",{className:"bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent",children:[" ","Extractor"]})]}),(0,a.jsx)("p",{className:"text-gray-400 mb-4 sm:mb-6 max-w-md text-sm sm:text-base",children:"The most advanced YouTube subtitle extraction tool. Extract, clean, and download subtitles from videos and playlists with professional-grade accuracy."}),(0,a.jsx)("div",{className:"flex space-x-3 sm:space-x-4",children:x.social.map(e=>(0,a.jsx)(i.P.a,{href:e.href,whileHover:{scale:1.1},whileTap:{scale:.95},className:"text-gray-400 hover:text-purple-400 transition-colors duration-200","aria-label":e.name,children:e.icon},e.name))})]}),(0,a.jsxs)(i.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1},viewport:{once:!0},children:[(0,a.jsx)("h4",{className:"text-base sm:text-lg font-semibold text-white mb-3 sm:mb-4",children:"Product"}),(0,a.jsx)("ul",{className:"space-y-1 sm:space-y-2",children:x.product.map(e=>(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:e.href,className:"text-gray-400 hover:text-purple-400 transition-colors duration-200 text-sm sm:text-base",children:e.name})},e.name))})]}),(0,a.jsxs)(i.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},viewport:{once:!0},children:[(0,a.jsx)("h4",{className:"text-base sm:text-lg font-semibold text-white mb-3 sm:mb-4",children:"Legal"}),(0,a.jsx)("ul",{className:"space-y-1 sm:space-y-2",children:x.legal.map(e=>(0,a.jsx)("li",{children:e.onClick?(0,a.jsx)("button",{onClick:e.onClick,className:"text-gray-400 hover:text-purple-400 transition-colors duration-200 text-sm sm:text-base",children:e.name}):(0,a.jsx)("a",{className:"text-gray-400 hover:text-purple-400 transition-colors duration-200 text-sm sm:text-base",children:e.name})},e.name))})]})]}),(0,a.jsxs)(i.P.div,{initial:{opacity:0},whileInView:{opacity:1},transition:{duration:.6,delay:.4},viewport:{once:!0},className:"flex flex-col lg:flex-row justify-between items-center pt-6 sm:pt-8 mt-6 sm:mt-8 border-t border-slate-800 space-y-4 lg:space-y-0",children:[(0,a.jsxs)("div",{className:"flex items-center text-gray-400 text-xs sm:text-sm text-center lg:text-left",children:[(0,a.jsxs)("span",{children:["\xa9 ",m," DownloadYTSubtitles.com. Made with"]}),(0,a.jsx)(n.A,{className:"w-3 h-3 sm:w-4 sm:h-4 text-red-400 mx-1"}),(0,a.jsx)("span",{children:"for the community."})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-4 lg:space-x-6 text-xs sm:text-sm text-gray-400",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(o.A,{className:"w-3 h-3 sm:w-4 sm:h-4"}),(0,a.jsx)("span",{children:"Privacy First"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(c.A,{className:"w-3 h-3 sm:w-4 sm:h-4"}),(0,a.jsx)("span",{children:"Professional Quality"})]})]})]})]})})}},5400:(e,t,s)=>{s.d(t,{R:()=>o});var a=s(4232),r=s(6171),i=s(427),l=s(9969),n=s(7685);let o=()=>{let{user:e}=(0,l.A)(),[t,s]=(0,a.useState)(null),[o,c]=(0,a.useState)(null),[d,m]=(0,a.useState)(!0),[x,p]=(0,a.useState)(null);(0,a.useEffect)(()=>{e?u():(s(null),c(null),m(!1))},[e]);let u=async()=>{if(e)try{m(!0),p(null);let{data:{session:e}}=await r.N.auth.getSession(),t=(null==e?void 0:e.access_token)?{Authorization:"Bearer ".concat(e.access_token)}:{},a=await fetch("/api/user/credits",{headers:{"Content-Type":"application/json",...t}});if(!a.ok)throw Error("Failed to fetch credit data");let i=await a.json();s(i.userCredits),c(i.usage)}catch(e){console.error("Error fetching credit data:",e),p("Failed to fetch credit data")}finally{m(!1)}},h=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;return!!t&&t.available_credits>=e},f=()=>t?t.available_credits:0,g=async t=>{if(!e)throw Error("User must be authenticated");try{let s=await fetch("/api/stripe/checkout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({priceId:i.D[t].priceId,userId:e.id,userEmail:e.email})});if(!s.ok)throw Error("Failed to create checkout session");let{sessionId:a}=await s.json();return a}catch(e){throw console.error("Error creating checkout session:",e),n.Ay.error("Failed to create checkout session"),e}},b=async()=>{throw n.Ay.error("Credit packs cannot be cancelled. Credits are valid for 6 months."),Error("Credit packs cannot be cancelled")},v=async()=>{await u()};return{subscription:t?{id:t.id,user_id:t.user_id,stripe_subscription_id:"",stripe_customer_id:"",status:t.available_credits>0?"active":"inactive",tier:"pro",current_period_start:t.created_at,current_period_end:t.last_purchase_date,cancel_at_period_end:!1,created_at:t.created_at,updated_at:t.updated_at}:null,usage:o,loading:d,error:x,canPerformAction:h,canExtractVideo:()=>h(1),getRemainingCredits:f,getRemainingExtractions:()=>f(),getUsageInfo:()=>t?{creditsPerAction:1,actionsPerExtraction:1}:null,shouldWarnAboutCredits:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=f();return t<=10||t<e},getUsageSuggestions:()=>{let e=f(),t=[];return e<=5&&t.push({type:"warning",message:"Low credits remaining. Consider purchasing more credits.",action:"upgrade"}),e<=2&&t.push({type:"error",message:"Very low credits. Use English default to save credits.",action:"use-english-default"}),0===e&&t.push({type:"error",message:"No credits remaining. Purchase a credit pack to continue.",action:"buy-credits"}),t},createCheckoutSession:g,cancelSubscription:b,refreshSubscription:v,userCredits:t}}},6960:(e,t,s)=>{s.d(t,{$:()=>c});var a=s(7876),r=s(4232),i=s(2987),l=s(9518),n=s(8647);let o=(0,l.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),c=r.forwardRef((e,t)=>{let{className:s,variant:r,size:l,asChild:c=!1,...d}=e,m=c?i.DX:"button";return(0,a.jsx)(m,{className:(0,n.cn)(o({variant:r,size:l,className:s})),ref:t,...d})});c.displayName="Button"},7855:(e,t,s)=>{s.d(t,{BK:()=>o,eu:()=>n,q5:()=>c});var a=s(7876),r=s(4232),i=s(4624),l=s(8647);let n=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(i.bL,{ref:t,className:(0,l.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",s),...r})});n.displayName=i.bL.displayName;let o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(i._V,{ref:t,className:(0,l.cn)("aspect-square h-full w-full",s),...r})});o.displayName=i._V.displayName;let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(i.H4,{ref:t,className:(0,l.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",s),...r})});c.displayName=i.H4.displayName},8606:(e,t,s)=>{s.d(t,{A:()=>z});var a=s(7876),r=s(1886),i=s(6960),l=s(9812),n=s(1340),o=s(2212),c=s(3846),d=s(5037),m=s(9969),x=s(3215),p=s(4232),u=s(2443),h=s(5259),f=s(8647);let g=u.bL,b=u.l9;u.YJ,u.ZL,u.Pb,u.z6,p.forwardRef((e,t)=>{let{className:s,inset:r,children:i,...l}=e;return(0,a.jsxs)(u.ZP,{ref:t,className:(0,f.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",r&&"pl-8",s),...l,children:[i,(0,a.jsx)(h.vKP,{className:"ml-auto h-4 w-4"})]})}).displayName=u.ZP.displayName,p.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(u.G5,{ref:t,className:(0,f.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...r})}).displayName=u.G5.displayName;let v=p.forwardRef((e,t)=>{let{className:s,sideOffset:r=4,...i}=e;return(0,a.jsx)(u.ZL,{children:(0,a.jsx)(u.UC,{ref:t,sideOffset:r,className:(0,f.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...i})})});v.displayName=u.UC.displayName;let w=p.forwardRef((e,t)=>{let{className:s,inset:r,...i}=e;return(0,a.jsx)(u.q7,{ref:t,className:(0,f.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r&&"pl-8",s),...i})});w.displayName=u.q7.displayName,p.forwardRef((e,t)=>{let{className:s,children:r,checked:i,...l}=e;return(0,a.jsxs)(u.H_,{ref:t,className:(0,f.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),checked:i,...l,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(u.VF,{children:(0,a.jsx)(h.Srz,{className:"h-4 w-4"})})}),r]})}).displayName=u.H_.displayName,p.forwardRef((e,t)=>{let{className:s,children:r,...i}=e;return(0,a.jsxs)(u.hN,{ref:t,className:(0,f.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...i,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(u.VF,{children:(0,a.jsx)(h.RiX,{className:"h-4 w-4 fill-current"})})}),r]})}).displayName=u.hN.displayName;let j=p.forwardRef((e,t)=>{let{className:s,inset:r,...i}=e;return(0,a.jsx)(u.JU,{ref:t,className:(0,f.cn)("px-2 py-1.5 text-sm font-semibold",r&&"pl-8",s),...i})});j.displayName=u.JU.displayName;let N=p.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(u.wv,{ref:t,className:(0,f.cn)("-mx-1 my-1 h-px bg-muted",s),...r})});N.displayName=u.wv.displayName;var y=s(7855),C=s(5400),k=s(4463),_=s(2671),A=s(1449),P=s(395);let E=e=>{var t,s,r,l,n;let{onNavigate:o}=e,{user:d,signOut:x}=(0,m.A)(),{subscription:p}=(0,C.R)();if(!d)return null;let u=(null==(t=d.user_metadata)?void 0:t.full_name)||(null==(s=d.user_metadata)?void 0:s.name)||(null==(r=d.email)?void 0:r.split("@")[0])||"User",h=(null==(l=d.user_metadata)?void 0:l.avatar_url)||(null==(n=d.user_metadata)?void 0:n.picture);return(0,a.jsxs)(g,{children:[(0,a.jsx)(b,{asChild:!0,children:(0,a.jsx)(i.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,a.jsxs)(y.eu,{className:"h-8 w-8",children:[(0,a.jsx)(y.BK,{src:h,alt:u}),(0,a.jsx)(y.q5,{className:"bg-purple-600 text-white",children:u.split(" ").map(e=>e[0]).join("").toUpperCase().slice(0,2)})]})})}),(0,a.jsxs)(v,{className:"w-56",align:"end",forceMount:!0,children:[(0,a.jsx)(j,{className:"font-normal",children:(0,a.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium leading-none",children:u}),(0,a.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:d.email}),p&&(0,a.jsxs)("div",{className:"flex items-center gap-1 mt-1",children:[(0,a.jsx)(k.A,{className:"w-3 h-3 text-yellow-500"}),(0,a.jsxs)("span",{className:"text-xs font-medium text-yellow-600 capitalize",children:[p.tier," Plan"]})]})]})}),(0,a.jsx)(N,{}),(0,a.jsxs)(w,{onClick:()=>o("dashboard"),children:[(0,a.jsx)(_.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Dashboard"})]}),(0,a.jsxs)(w,{onClick:()=>o("pricing"),children:[(0,a.jsx)(c.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Subscription"})]}),(0,a.jsxs)(w,{onClick:()=>o("settings"),children:[(0,a.jsx)(A.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Settings"})]}),(0,a.jsx)(N,{}),(0,a.jsxs)(w,{onClick:x,children:[(0,a.jsx)(P.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Sign out"})]})]})]})},z=e=>{let{currentView:t,onNavigate:s,onFeedback:p}=e,{user:u}=(0,m.A)();return(0,a.jsx)(r.P.header,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"bg-slate-900/95 backdrop-blur-sm border-b border-slate-700 sticky top-0 z-50",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-3 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between h-14 sm:h-16",children:[(0,a.jsxs)(r.P.div,{whileHover:{scale:1.05},className:"flex items-center space-x-2 sm:space-x-3 cursor-pointer",onClick:()=>s(""),children:[(0,a.jsx)("div",{className:"w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)(l.A,{className:"w-4 h-4 sm:w-6 sm:h-6 text-white"})}),(0,a.jsxs)("div",{className:"hidden xs:block",children:[(0,a.jsx)("h1",{className:"text-lg sm:text-xl font-bold text-white",children:"DownloadYTSubtitles"}),(0,a.jsx)("p",{className:"text-xs text-gray-400 hidden sm:block",children:"YouTube Subtitle Extractor"})]}),(0,a.jsx)("div",{className:"block xs:hidden",children:(0,a.jsx)("h1",{className:"text-sm font-bold text-white",children:"DYTS"})})]}),(0,a.jsxs)("nav",{className:"hidden md:flex items-center space-x-6",children:[(0,a.jsxs)(i.$,{variant:"landing"===t?"default":"ghost",size:"sm",onClick:()=>s(""),className:"landing"===t?"bg-purple-600 hover:bg-purple-700":"text-gray-300 hover:text-white",children:[(0,a.jsx)(n.A,{className:"w-4 h-4 mr-2"}),"Home"]}),(0,a.jsxs)(i.$,{variant:"extractor"===t?"default":"ghost",size:"sm",onClick:()=>s("extractor"),className:"extractor"===t?"bg-purple-600 hover:bg-purple-700":"text-gray-300 hover:text-white",children:[(0,a.jsx)(l.A,{className:"w-4 h-4 mr-2"}),"Extract"]}),(0,a.jsxs)(i.$,{variant:"faq"===t?"default":"ghost",size:"sm",onClick:()=>s("faq"),className:"faq"===t?"bg-purple-600 hover:bg-purple-700":"text-gray-300 hover:text-white",children:[(0,a.jsx)(o.A,{className:"w-4 h-4 mr-2"}),"FAQ"]}),(0,a.jsxs)(i.$,{variant:"pricing"===t?"default":"ghost",size:"sm",onClick:()=>s("pricing"),className:"pricing"===t?"bg-purple-600 hover:bg-purple-700":"text-gray-300 hover:text-white",children:[(0,a.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"Pricing"]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 sm:space-x-3",children:[(0,a.jsxs)(i.$,{variant:"outline",size:"sm",onClick:p,className:"border-purple-500 text-purple-400 hover:bg-purple-500 hover:text-white text-xs sm:text-sm px-2 sm:px-3",children:[(0,a.jsx)(d.A,{className:"w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2"}),(0,a.jsx)("span",{className:"hidden xs:inline",children:"Feedback"}),(0,a.jsx)("span",{className:"xs:hidden",children:"FB"})]}),u?(0,a.jsx)(E,{onNavigate:s}):(0,a.jsx)(x.A,{variant:"outline",size:"sm",className:"border-purple-600 text-purple-400 hover:bg-purple-600 hover:text-white"}),(0,a.jsx)("div",{className:"md:hidden",children:(0,a.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>s("extractor"),className:"text-gray-300 hover:text-white p-2",children:(0,a.jsx)(l.A,{className:"w-4 h-4 sm:w-5 sm:h-5"})})})]})]}),(0,a.jsx)("div",{className:"md:hidden pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2 overflow-x-auto",children:[(0,a.jsx)(i.$,{variant:"landing"===t?"default":"ghost",size:"sm",onClick:()=>s(""),className:"text-xs px-3 py-2 ".concat("landing"===t?"bg-purple-600 hover:bg-purple-700":"text-gray-300 hover:text-white"),children:"Home"}),(0,a.jsx)(i.$,{variant:"extractor"===t?"default":"ghost",size:"sm",onClick:()=>s("extractor"),className:"text-xs px-3 py-2 ".concat("extractor"===t?"bg-purple-600 hover:bg-purple-700":"text-gray-300 hover:text-white"),children:"Extract"}),(0,a.jsx)(i.$,{variant:"faq"===t?"default":"ghost",size:"sm",onClick:()=>s("faq"),className:"text-xs px-3 py-2 ".concat("faq"===t?"bg-purple-600 hover:bg-purple-700":"text-gray-300 hover:text-white"),children:"FAQ"}),(0,a.jsx)(i.$,{variant:"pricing"===t?"default":"ghost",size:"sm",onClick:()=>s("pricing"),className:"text-xs px-3 py-2 ".concat("pricing"===t?"bg-purple-600 hover:bg-purple-700":"text-gray-300 hover:text-white"),children:"Pricing"})]})})]})})}},8638:(e,t,s)=>{s.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>o,Zp:()=>l,aR:()=>n,wL:()=>m});var a=s(7876),r=s(4232),i=s(8647);let l=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("rounded-xl border bg-card text-card-foreground shadow",s),...r})});l.displayName="Card";let n=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",s),...r})});n.displayName="CardHeader";let o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("h3",{ref:t,className:(0,i.cn)("font-semibold leading-none tracking-tight",s),...r})});o.displayName="CardTitle";let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",s),...r})});c.displayName="CardDescription";let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",s),...r})});d.displayName="CardContent";let m=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",s),...r})});m.displayName="CardFooter"},8647:(e,t,s)=>{s.d(t,{cn:()=>i});var a=s(9241),r=s(9573);function i(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}}}]);