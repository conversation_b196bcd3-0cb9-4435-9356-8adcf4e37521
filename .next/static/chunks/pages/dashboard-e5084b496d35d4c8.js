(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[724],{427:(e,s,t)=>{"use strict";t.d(s,{D:()=>i,t:()=>l});var a=t(4241);let r="pk_test_51Rbz5DG3dq2cZKhWQAhsMvCKvlJ7qki27vhezUpnbMECmp53uG6V3JmEy7pQodTCBWYNKeP55X64ZIJxaXfkijrM00xhD0btGs";r||console.warn("Missing NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY environment variable");let l=r?(0,a.c)(r):null,i={starter:{name:"Starter",price:10,priceId:"price_1Rbz9CG3dq2cZKhWCnIVHUx6",features:["50 credits (~50 subtitle downloads)","VTT and TXT format downloads","Manual and Auto-generated caption support","English subtitle extraction","Multi-language support","Credits valid for 6 months"],limits:{creditsPerMonth:50,creditsPerAction:1,actionsPerExtraction:1},popular:!1},pro:{name:"Pro",price:30,priceId:"price_1RbzCCG3dq2cZKhWS3MlXiCZ",features:["200 credits (~200 subtitle downloads)","VTT and TXT format downloads","Manual and Auto-generated caption support","English subtitle extraction","Multi-language support","Credits valid for 6 months"],limits:{creditsPerMonth:200,creditsPerAction:1,actionsPerExtraction:1},popular:!0},creator:{name:"Creator",price:75,priceId:"price_1RbzDHG3dq2cZKhWID6C0aMY",features:["600 credits (~600 subtitle downloads)","VTT and TXT format downloads","Manual and Auto-generated caption support","English subtitle extraction","Multi-language support","Credits valid for 6 months"],limits:{creditsPerMonth:600,creditsPerAction:1,actionsPerExtraction:1},popular:!1}}},5101:(e,s,t)=>{"use strict";t.d(s,{E:()=>n});var a=t(7876);t(4232);var r=t(9518),l=t(8647);let i=(0,r.F)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function n(e){let{className:s,variant:t,...r}=e;return(0,a.jsx)("div",{className:(0,l.cn)(i({variant:t}),s),...r})}},5400:(e,s,t)=>{"use strict";t.d(s,{R:()=>c});var a=t(4232),r=t(6171),l=t(427),i=t(9969),n=t(7685);let c=()=>{let{user:e}=(0,i.A)(),[s,t]=(0,a.useState)(null),[c,d]=(0,a.useState)(null),[o,u]=(0,a.useState)(!0),[m,x]=(0,a.useState)(null);(0,a.useEffect)(()=>{e?h():(t(null),d(null),u(!1))},[e]);let h=async()=>{if(e)try{u(!0),x(null);let{data:{session:e}}=await r.N.auth.getSession(),s=(null==e?void 0:e.access_token)?{Authorization:"Bearer ".concat(e.access_token)}:{},a=await fetch("/api/user/credits",{headers:{"Content-Type":"application/json",...s}});if(!a.ok)throw Error("Failed to fetch credit data");let l=await a.json();t(l.userCredits),d(l.usage)}catch(e){console.error("Error fetching credit data:",e),x("Failed to fetch credit data")}finally{u(!1)}},p=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;return!!s&&s.available_credits>=e},g=()=>s?s.available_credits:0,f=async s=>{if(!e)throw Error("User must be authenticated");try{let t=await fetch("/api/stripe/checkout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({priceId:l.D[s].priceId,userId:e.id,userEmail:e.email})});if(!t.ok)throw Error("Failed to create checkout session");let{sessionId:a}=await t.json();return a}catch(e){throw console.error("Error creating checkout session:",e),n.Ay.error("Failed to create checkout session"),e}},b=async()=>{throw n.Ay.error("Credit packs cannot be cancelled. Credits are valid for 6 months."),Error("Credit packs cannot be cancelled")},v=async()=>{await h()};return{subscription:s?{id:s.id,user_id:s.user_id,stripe_subscription_id:"",stripe_customer_id:"",status:s.available_credits>0?"active":"inactive",tier:"pro",current_period_start:s.created_at,current_period_end:s.last_purchase_date,cancel_at_period_end:!1,created_at:s.created_at,updated_at:s.updated_at}:null,usage:c,loading:o,error:m,canPerformAction:p,canExtractVideo:()=>p(1),getRemainingCredits:g,getRemainingExtractions:()=>g(),getUsageInfo:()=>s?{creditsPerAction:1,actionsPerExtraction:1}:null,shouldWarnAboutCredits:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,s=g();return s<=10||s<e},getUsageSuggestions:()=>{let e=g(),s=[];return e<=5&&s.push({type:"warning",message:"Low credits remaining. Consider purchasing more credits.",action:"upgrade"}),e<=2&&s.push({type:"error",message:"Very low credits. Use English default to save credits.",action:"use-english-default"}),0===e&&s.push({type:"error",message:"No credits remaining. Purchase a credit pack to continue.",action:"buy-credits"}),s},createCheckoutSession:f,cancelSubscription:b,refreshSubscription:v,userCredits:s}}},6752:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>V});var a=t(7876),r=t(7328),l=t.n(r),i=t(9099),n=t(4232),c=t(1886),d=t(6960),o=t(8638),u=t(491),m=t(8647);let x=u.bL,h=n.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(u.B8,{ref:s,className:(0,m.cn)("inline-flex h-9 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground",t),...r})});h.displayName=u.B8.displayName;let p=n.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(u.l9,{ref:s,className:(0,m.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow",t),...r})});p.displayName=u.l9.displayName;let g=n.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(u.UC,{ref:s,className:(0,m.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...r})});g.displayName=u.UC.displayName;var f=t(9969),b=t(5400),v=t(5101),j=t(9665);let y=n.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,role:"alert",className:(0,m.cn)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",t),...r})});y.displayName="Alert",n.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("h5",{ref:s,className:(0,m.cn)("mb-1 font-medium leading-none tracking-tight",t),...r})}).displayName="AlertTitle";let N=n.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,m.cn)("text-sm [&_p]:leading-relaxed",t),...r})});N.displayName="AlertDescription";var w=t(5851),_=t(4463),k=t(3654),C=t(1899),A=t(8572),T=t(9468);let E=()=>{let{userCredits:e,usage:s,loading:t,shouldWarnAboutCredits:r,getUsageSuggestions:l,getRemainingCredits:i}=(0,b.R)();if(t)return(0,a.jsx)(o.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700",children:(0,a.jsx)(o.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"animate-pulse space-y-4",children:[(0,a.jsx)("div",{className:"h-4 bg-slate-700 rounded w-1/3"}),(0,a.jsx)("div",{className:"h-8 bg-slate-700 rounded w-1/2"}),(0,a.jsx)("div",{className:"h-4 bg-slate-700 rounded w-2/3"})]})})});if(!e)return(0,a.jsx)(o.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700",children:(0,a.jsxs)(o.aR,{children:[(0,a.jsxs)(o.ZB,{className:"text-white flex items-center gap-2",children:[(0,a.jsx)(w.A,{className:"w-5 h-5 text-yellow-500"}),"No Credits Available"]}),(0,a.jsx)(o.BT,{className:"text-gray-300",children:"Purchase a credit pack to start extracting YouTube subtitles"})]})});let n=e.used_credits||0,c=e.total_credits||0,d=e.available_credits||0;return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(o.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700",children:[(0,a.jsxs)(o.aR,{children:[(0,a.jsxs)(o.ZB,{className:"text-white flex items-center gap-2",children:[(0,a.jsx)(_.A,{className:"w-5 h-5 text-yellow-500"}),"Credit Balance"]}),(0,a.jsxs)(o.BT,{className:"text-gray-300 flex items-center gap-2",children:[(0,a.jsx)(v.E,{className:"bg-green-600",children:"Active"}),(0,a.jsxs)("span",{children:[d," credits available"]})]})]}),(0,a.jsx)(o.Wu,{className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-400 mb-1",children:[(0,a.jsx)(k.A,{className:"w-4 h-4"}),"Last Purchase"]}),(0,a.jsx)("div",{className:"text-white",children:e.last_purchase_date?(0,T.GP)(new Date(e.last_purchase_date),"MMM d, yyyy"):"No purchases yet"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-400 mb-1",children:[(0,a.jsx)(C.A,{className:"w-4 h-4"}),"Credits Expire"]}),(0,a.jsx)("div",{className:"text-white",children:e.last_purchase_date?(0,T.GP)(new Date(new Date(e.last_purchase_date).getTime()+15552e6),"MMM d, yyyy"):"N/A"})]})]})})]}),(0,a.jsxs)(o.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700",children:[(0,a.jsxs)(o.aR,{children:[(0,a.jsx)(o.ZB,{className:"text-white",children:"Usage This Month"}),(0,a.jsx)(o.BT,{className:"text-gray-300",children:"Track your credit usage and remaining extractions"})]}),(0,a.jsxs)(o.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-400",children:"Credits Used"}),(0,a.jsxs)("span",{className:"text-white font-medium",children:[n," / ",c]})]}),(0,a.jsx)(j.k,{value:0===c?0:n/c*100,className:"h-2"}),(0,a.jsxs)("div",{className:"flex justify-between items-center mt-2 text-xs text-gray-500",children:[(0,a.jsxs)("span",{children:[d," credits remaining"]}),(0,a.jsxs)("span",{children:["~",d," subtitle downloads left"]})]})]}),(0,a.jsxs)("div",{className:"bg-slate-700/50 rounded-lg p-3",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-white mb-2",children:"Credit System"}),(0,a.jsxs)("div",{className:"space-y-1 text-xs text-gray-400",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"English subtitle download:"}),(0,a.jsx)("span",{children:"1 credit"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Browse languages (optional):"}),(0,a.jsx)("span",{children:"1 credit"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Download specific language:"}),(0,a.jsx)("span",{children:"1 credit"})]}),(0,a.jsxs)("div",{className:"flex justify-between border-t border-slate-600 pt-1 mt-1 font-medium text-gray-300",children:[(0,a.jsx)("span",{children:"Download all languages:"}),(0,a.jsx)("span",{children:"1 credit"})]})]})]}),l().length>0&&(0,a.jsx)("div",{className:"space-y-2",children:l().map((e,s)=>(0,a.jsxs)(y,{className:"border ".concat("error"===e.type?"border-red-700/30 bg-red-900/20":"warning"===e.type?"border-yellow-700/30 bg-yellow-900/20":"border-blue-700/30 bg-blue-900/20"),children:["error"===e.type?(0,a.jsx)(w.A,{className:"h-4 w-4 text-red-400"}):"warning"===e.type?(0,a.jsx)(w.A,{className:"h-4 w-4 text-yellow-400"}):(0,a.jsx)(A.A,{className:"h-4 w-4 text-blue-400"}),(0,a.jsx)(N,{className:"text-sm ".concat("error"===e.type?"text-red-300":"warning"===e.type?"text-yellow-300":"text-blue-300"),children:e.message})]},s))}),(0,a.jsxs)("div",{className:"pt-4 border-t border-slate-700",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-white mb-2",children:"Credit Features"}),(0,a.jsxs)("ul",{className:"space-y-1 text-sm text-gray-400",children:[(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-1.5 h-1.5 bg-green-500 rounded-full"}),"VTT and TXT format downloads"]}),(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-1.5 h-1.5 bg-green-500 rounded-full"}),"Auto-generated caption support"]}),(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-1.5 h-1.5 bg-green-500 rounded-full"}),"Credits valid for 6 months"]})]})]})]})]})]})};var R=t(2885),P=t(2671),B=t(3846),M=t(1449),S=t(9812),Z=t(7855),D=t(7685);let U=e=>{var s,t,r,l,u;let{onBack:m,onNavigate:v}=e,j=(0,i.useRouter)(),{user:y}=(0,f.A)(),{subscription:N,cancelSubscription:w,refreshSubscription:_}=(0,b.R)(),[k,C]=n.useState(!1);if(n.useEffect(()=>{let{session_id:e}=j.query;if(e&&"string"==typeof e){D.Ay.success("Payment successful! Your credits have been added."),_();let e=j.asPath.split("?")[0];j.replace(e,void 0,{shallow:!0})}},[j.query,_,j]),!y)return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center",children:(0,a.jsx)(o.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700 p-8",children:(0,a.jsxs)(o.Wu,{className:"text-center",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-white mb-4",children:"Access Denied"}),(0,a.jsx)("p",{className:"text-gray-300 mb-4",children:"Please sign in to access your dashboard."}),(0,a.jsx)(d.$,{onClick:m,variant:"outline",children:"Go Back"})]})})});let A=(null==(s=y.user_metadata)?void 0:s.full_name)||(null==(t=y.user_metadata)?void 0:t.name)||(null==(r=y.email)?void 0:r.split("@")[0])||"User",T=(null==(l=y.user_metadata)?void 0:l.avatar_url)||(null==(u=y.user_metadata)?void 0:u.picture),U=async()=>{if(N&&window.confirm("Are you sure you want to cancel your subscription? You will continue to have access until the end of your current billing period."))try{C(!0),await w()}catch(e){console.error("Error cancelling subscription:",e)}finally{C(!1)}};return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900",children:(0,a.jsxs)("div",{className:"max-w-6xl mx-auto px-3 sm:px-6 lg:px-8 py-12",children:[m&&(0,a.jsx)(c.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:"mb-8",children:(0,a.jsxs)(d.$,{onClick:m,variant:"ghost",className:"text-gray-300 hover:text-white",children:[(0,a.jsx)(R.A,{className:"w-4 h-4 mr-2"}),"Back to Home"]})}),(0,a.jsx)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},className:"mb-8",children:(0,a.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[(0,a.jsxs)(Z.eu,{className:"h-16 w-16",children:[(0,a.jsx)(Z.BK,{src:T,alt:A}),(0,a.jsx)(Z.q5,{className:"bg-purple-600 text-white text-lg",children:A.split(" ").map(e=>e[0]).join("").toUpperCase().slice(0,2)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-white",children:A}),(0,a.jsx)("p",{className:"text-gray-300",children:y.email})]})]})}),(0,a.jsx)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},children:(0,a.jsxs)(x,{defaultValue:"overview",className:"space-y-6",children:[(0,a.jsxs)(h,{className:"grid w-full grid-cols-3 bg-slate-800/80 backdrop-blur-sm border border-slate-700",children:[(0,a.jsxs)(p,{value:"overview",className:"data-[state=active]:bg-purple-600",children:[(0,a.jsx)(P.A,{className:"w-4 h-4 mr-2"}),"Overview"]}),(0,a.jsxs)(p,{value:"subscription",className:"data-[state=active]:bg-purple-600",children:[(0,a.jsx)(B.A,{className:"w-4 h-4 mr-2"}),"Subscription"]}),(0,a.jsxs)(p,{value:"settings",className:"data-[state=active]:bg-purple-600",children:[(0,a.jsx)(M.A,{className:"w-4 h-4 mr-2"}),"Settings"]})]}),(0,a.jsx)(g,{value:"overview",className:"space-y-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsx)(E,{}),(0,a.jsxs)(o.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700",children:[(0,a.jsxs)(o.aR,{children:[(0,a.jsxs)(o.ZB,{className:"text-white flex items-center gap-2",children:[(0,a.jsx)(S.A,{className:"w-5 h-5"}),"Quick Actions"]}),(0,a.jsx)(o.BT,{className:"text-gray-300",children:"Common tasks and shortcuts"})]}),(0,a.jsxs)(o.Wu,{className:"space-y-3",children:[(0,a.jsx)(d.$,{onClick:()=>null==v?void 0:v("extractor"),className:"w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700",children:"Extract Subtitles"}),(0,a.jsx)(d.$,{onClick:()=>null==v?void 0:v("pricing"),variant:"outline",className:"w-full border-slate-600 text-gray-300 hover:text-white",children:"View Plans"}),(0,a.jsx)(d.$,{onClick:()=>null==v?void 0:v("faq"),variant:"ghost",className:"w-full text-gray-300 hover:text-white",children:"Help & FAQ"})]})]})]})}),(0,a.jsx)(g,{value:"subscription",className:"space-y-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsx)("div",{className:"lg:col-span-2",children:(0,a.jsx)(E,{})}),(0,a.jsxs)(o.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700",children:[(0,a.jsxs)(o.aR,{children:[(0,a.jsx)(o.ZB,{className:"text-white",children:"Manage Subscription"}),(0,a.jsx)(o.BT,{className:"text-gray-300",children:"Update your plan or billing information"})]}),(0,a.jsxs)(o.Wu,{className:"space-y-3",children:[(0,a.jsx)(d.$,{onClick:()=>null==v?void 0:v("pricing"),className:"w-full bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700",children:"Change Plan"}),N&&"active"===N.status&&!N.cancel_at_period_end&&(0,a.jsx)(d.$,{onClick:U,disabled:k,variant:"destructive",className:"w-full",children:k?"Cancelling...":"Cancel Subscription"}),(null==N?void 0:N.cancel_at_period_end)&&(0,a.jsx)("div",{className:"text-sm text-yellow-400 p-3 bg-yellow-400/10 rounded-lg border border-yellow-400/20",children:"Your subscription will cancel at the end of the current billing period."})]})]})]})}),(0,a.jsx)(g,{value:"settings",className:"space-y-6",children:(0,a.jsxs)(o.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700",children:[(0,a.jsxs)(o.aR,{children:[(0,a.jsx)(o.ZB,{className:"text-white",children:"Account Settings"}),(0,a.jsx)(o.BT,{className:"text-gray-300",children:"Manage your account preferences"})]}),(0,a.jsxs)(o.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-300",children:"Email"}),(0,a.jsx)("div",{className:"text-white bg-slate-700 p-3 rounded-lg",children:y.email})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-300",children:"Full Name"}),(0,a.jsx)("div",{className:"text-white bg-slate-700 p-3 rounded-lg",children:A})]}),(0,a.jsx)("div",{className:"pt-4 border-t border-slate-700",children:(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Account settings are managed through your Google account. Changes made there will be reflected here automatically."})})]})]})})]})})]})})};function V(){let e=(0,i.useRouter)();return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(l(),{children:[(0,a.jsx)("title",{children:"Dashboard - DownloadYTSubtitles"}),(0,a.jsx)("meta",{name:"description",content:"User dashboard for DownloadYTSubtitles - YouTube subtitle extractor service."}),(0,a.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes"}),(0,a.jsx)("link",{rel:"icon",href:"/favicon.ico"})]}),(0,a.jsx)(U,{onBack:()=>{e.push("/")},onNavigate:s=>{e.push("/".concat(s))}})]})}},6960:(e,s,t)=>{"use strict";t.d(s,{$:()=>d});var a=t(7876),r=t(4232),l=t(2987),i=t(9518),n=t(8647);let c=(0,i.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),d=r.forwardRef((e,s)=>{let{className:t,variant:r,size:i,asChild:d=!1,...o}=e,u=d?l.DX:"button";return(0,a.jsx)(u,{className:(0,n.cn)(c({variant:r,size:i,className:t})),ref:s,...o})});d.displayName="Button"},7580:(e,s,t)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/dashboard",function(){return t(6752)}])},7855:(e,s,t)=>{"use strict";t.d(s,{BK:()=>c,eu:()=>n,q5:()=>d});var a=t(7876),r=t(4232),l=t(4624),i=t(8647);let n=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.bL,{ref:s,className:(0,i.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",t),...r})});n.displayName=l.bL.displayName;let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l._V,{ref:s,className:(0,i.cn)("aspect-square h-full w-full",t),...r})});c.displayName=l._V.displayName;let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.H4,{ref:s,className:(0,i.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",t),...r})});d.displayName=l.H4.displayName},8638:(e,s,t)=>{"use strict";t.d(s,{BT:()=>d,Wu:()=>o,ZB:()=>c,Zp:()=>i,aR:()=>n,wL:()=>u});var a=t(7876),r=t(4232),l=t(8647);let i=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("rounded-xl border bg-card text-card-foreground shadow",t),...r})});i.displayName="Card";let n=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",t),...r})});n.displayName="CardHeader";let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("h3",{ref:s,className:(0,l.cn)("font-semibold leading-none tracking-tight",t),...r})});c.displayName="CardTitle";let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("p",{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",t),...r})});d.displayName="CardDescription";let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("p-6 pt-0",t),...r})});o.displayName="CardContent";let u=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("flex items-center p-6 pt-0",t),...r})});u.displayName="CardFooter"},8647:(e,s,t)=>{"use strict";t.d(s,{cn:()=>l});var a=t(9241),r=t(9573);function l(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,r.QP)((0,a.$)(s))}},9665:(e,s,t)=>{"use strict";t.d(s,{k:()=>n});var a=t(7876),r=t(4232),l=t(4972),i=t(8647);let n=r.forwardRef((e,s)=>{let{className:t,value:r,...n}=e;return(0,a.jsx)(l.bL,{ref:s,className:(0,i.cn)("relative h-2 w-full overflow-hidden rounded-full bg-primary/20",t),...n,children:(0,a.jsx)(l.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(r||0),"%)")}})})});n.displayName=l.bL.displayName}},e=>{var s=s=>e(e.s=s);e.O(0,[893,715,636,593,792],()=>s(7580)),_N_E=e.O()}]);