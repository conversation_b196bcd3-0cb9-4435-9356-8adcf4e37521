(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[357],{2885:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(1713).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},5851:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(1713).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},5930:(e,t,s)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/disclaimer",function(){return s(8263)}])},8263:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>g});var a=s(7876),i=s(7328),r=s.n(i),n=s(9099),l=s(8606),o=s(4518),c=s(1886),d=s(8638),m=s(6960),h=s(8572),u=s(7297),p=s(5390),x=s(5851),y=s(2885);let b=e=>{let{onBack:t}=e,s=[{title:"Service Purpose",icon:(0,a.jsx)(h.A,{className:"w-5 h-5"}),content:["DownloadYTSubtitles.com is a free tool designed to extract publicly available subtitle data from YouTube videos.","We provide this service to help users access subtitle content for educational, research, and accessibility purposes.","Our tool only processes subtitle files that are already publicly accessible through YouTube's platform."]},{title:"Content Ownership",icon:(0,a.jsx)(u.A,{className:"w-5 h-5"}),content:["All subtitle content extracted through our service belongs to the original video creators and YouTube.","We do not claim ownership of any extracted subtitle content or the underlying video material.","Users are responsible for respecting copyright laws and intellectual property rights when using extracted content.","Always credit the original content creators when using their subtitle content."]},{title:"Acceptable Use",icon:(0,a.jsx)(p.A,{className:"w-5 h-5"}),content:["This service is intended for personal, educational, and non-commercial use only.","Commercial use of extracted subtitles may require permission from the original content creators.","Users must comply with YouTube's Terms of Service and applicable copyright laws.","Fair use principles should guide your use of extracted subtitle content."]},{title:"Service Limitations",icon:(0,a.jsx)(x.A,{className:"w-5 h-5"}),content:["We cannot guarantee the availability or accuracy of subtitle content for all videos.","Subtitle availability depends on whether the video creator has enabled subtitles or YouTube has generated them.","Auto-generated subtitles may contain errors and should be reviewed before use.","Service availability may be interrupted for maintenance or technical reasons."]},{title:"Legal Compliance",icon:(0,a.jsx)(u.A,{className:"w-5 h-5"}),content:["Users are responsible for ensuring their use of extracted content complies with local laws.","Different countries may have varying copyright and fair use regulations.","Educational and research use is generally more permissible than commercial use.","When in doubt, seek permission from the original content creator."]},{title:"No Warranty",icon:(0,a.jsx)(x.A,{className:"w-5 h-5"}),content:["This service is provided 'as is' without any warranties or guarantees.","We do not warrant the accuracy, completeness, or quality of extracted subtitles.","Users assume all risks associated with the use of this service.","We are not liable for any damages resulting from the use of extracted content."]}];return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)(c.P.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"flex items-center justify-between mb-8",children:[(0,a.jsxs)(m.$,{variant:"ghost",onClick:t,className:"text-white hover:text-purple-400",children:[(0,a.jsx)(y.A,{className:"w-4 h-4 mr-2"}),"Back to Home"]}),(0,a.jsx)("div",{className:"text-right",children:(0,a.jsxs)("p",{className:"text-gray-400 text-sm",children:["Last updated: ",new Date().toLocaleDateString()]})})]}),(0,a.jsxs)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},className:"text-center mb-12",children:[(0,a.jsx)("h1",{className:"text-4xl md:text-5xl font-bold text-white mb-4",children:"Disclaimer"}),(0,a.jsx)("p",{className:"text-xl text-gray-300 max-w-2xl mx-auto",children:"Important information about using DownloadYTSubtitles.com responsibly and legally"})]}),(0,a.jsx)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},className:"mb-8",children:(0,a.jsx)(d.Zp,{className:"bg-orange-900/20 border-orange-500/30",children:(0,a.jsx)(d.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,a.jsx)(x.A,{className:"w-8 h-8 text-orange-400 flex-shrink-0 mt-1"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-orange-400 mb-2",children:"Important Notice"}),(0,a.jsxs)("p",{className:"text-gray-300",children:[(0,a.jsx)("strong",{children:"Use Responsibly:"})," This tool extracts publicly available subtitle data. Users are responsible for ensuring their use complies with copyright laws and YouTube's Terms of Service. Always respect content creators' rights and give proper attribution."]})]})]})})})}),(0,a.jsx)("div",{className:"space-y-6",children:s.map((e,t)=>(0,a.jsx)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*(t+3)},children:(0,a.jsxs)(d.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700",children:[(0,a.jsx)(d.aR,{children:(0,a.jsxs)(d.ZB,{className:"text-white flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"text-purple-400",children:e.icon}),(0,a.jsx)("span",{children:e.title})]})}),(0,a.jsx)(d.Wu,{children:(0,a.jsx)("div",{className:"space-y-3",children:e.content.map((e,t)=>(0,a.jsx)("p",{className:"text-gray-300 leading-relaxed",children:e},t))})})]})},t))}),(0,a.jsx)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},className:"mt-12 text-center",children:(0,a.jsx)(d.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700",children:(0,a.jsxs)(d.Wu,{className:"p-6",children:[(0,a.jsx)("p",{className:"text-gray-300 mb-4",children:"By using DownloadYTSubtitles.com, you acknowledge that you have read, understood, and agree to comply with this disclaimer and use the service responsibly."}),(0,a.jsxs)("p",{className:"text-sm text-gray-400",children:["This disclaimer is effective as of ",new Date().toLocaleDateString()," and may be updated from time to time to reflect changes in our service or legal requirements."]})]})})})]})})};function g(){let e=(0,n.useRouter)();return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(r(),{children:[(0,a.jsx)("title",{children:"Disclaimer - DownloadYTSubtitles"}),(0,a.jsx)("meta",{name:"description",content:"Disclaimer for DownloadYTSubtitles - YouTube subtitle extractor service."}),(0,a.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes"}),(0,a.jsx)("link",{rel:"icon",href:"/favicon.ico"})]}),(0,a.jsxs)("div",{className:"min-h-screen font-sans",children:[(0,a.jsx)(l.A,{currentView:"disclaimer",onNavigate:t=>{e.push("/".concat(t))},onFeedback:()=>{window.open("https://docs.google.com/forms/d/e/1FAIpQLSd1zn2jbwo7UKAkoRSPIV2RIxt2ZNi4VQYLqC8S0bV7CRZe_Q/viewform?usp=dialog","_blank")}}),(0,a.jsx)(b,{onBack:()=>{e.push("/")}}),(0,a.jsx)(o.A,{onTermsClick:()=>{e.push("/terms")},onPrivacyClick:()=>{e.push("/privacy")},onDisclaimerClick:()=>{e.push("/disclaimer")}})]})]})}},8572:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(1713).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[587,893,817,701,636,593,792],()=>t(5930)),_N_E=e.O()}]);