(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[18],{2467:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(1713).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},2885:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(1713).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},4049:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>C});var a=s(7876),r=s(7328),i=s.n(r),l=s(9099),n=s(8606),c=s(4518),d=s(4232),o=s(1886),x=s(6960),m=s(8638),h=s(5101),u=s(2467),p=s(6124),b=s(4463);let g=(0,s(1713).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);var j=s(427),f=s(9969),y=s(5400),v=s(7685);let N=e=>{let{tier:t,isCurrentPlan:s=!1,onSelectPlan:r}=e,{user:i}=(0,f.A)(),{createCheckoutSession:l}=(0,y.R)(),[n,c]=d.useState(!1),N=j.D[t],w=async()=>{if(!i)return void v.Ay.error("Please sign in to subscribe");if(s)return void(0,v.Ay)("This is your current plan");try{c(!0);let e=await l(t),s=await j.t;if(!s)throw Error("Stripe failed to load");let{error:a}=await s.redirectToCheckout({sessionId:e});if(a)throw a}catch(e){console.error("Error selecting plan:",e),v.Ay.error("Failed to start checkout process")}finally{c(!1)}};return(0,a.jsx)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"relative",children:(0,a.jsxs)(m.Zp,{className:"relative overflow-hidden ".concat(N.popular?"border-purple-500 shadow-lg shadow-purple-500/20":"border-slate-700"," bg-slate-800/80 backdrop-blur-sm"),children:[N.popular&&(0,a.jsx)("div",{className:"absolute top-0 left-0 right-0",children:(0,a.jsx)("div",{className:"bg-gradient-to-r from-purple-500 to-pink-500 text-white text-center py-2 text-sm font-medium",children:"Most Popular"})}),(0,a.jsxs)(m.aR,{className:N.popular?"pt-12":"pt-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(()=>{switch(t){case"starter":return(0,a.jsx)(u.A,{className:"w-6 h-6 text-blue-500"});case"pro":return(0,a.jsx)(p.A,{className:"w-6 h-6 text-purple-500"});case"creator":return(0,a.jsx)(b.A,{className:"w-6 h-6 text-yellow-500"});default:return(0,a.jsx)(u.A,{className:"w-6 h-6"})}})(),(0,a.jsx)(m.ZB,{className:"text-white",children:N.name})]}),s&&(0,a.jsx)(h.E,{variant:"secondary",className:"bg-green-600 text-white",children:"Current Plan"})]}),(0,a.jsx)(m.BT,{className:"text-gray-300",children:(0,a.jsxs)("div",{className:"flex items-baseline gap-1",children:[(0,a.jsxs)("span",{className:"text-3xl font-bold text-white",children:["$",N.price]}),(0,a.jsx)("span",{className:"text-gray-400",children:"one-time"})]})})]}),(0,a.jsxs)(m.Wu,{className:"space-y-4",children:[(0,a.jsx)("ul",{className:"space-y-3",children:N.features.map((e,t)=>(0,a.jsxs)("li",{className:"flex items-start gap-2",children:[(0,a.jsx)(g,{className:"w-5 h-5 text-green-500 mt-0.5 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-gray-300 text-sm",children:e})]},t))}),(0,a.jsxs)("div",{className:"pt-4 border-t border-slate-700",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-white mb-2",children:"Credit Pack Details"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm text-gray-400",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Total credits:"}),(0,a.jsx)("span",{className:"text-white font-medium",children:N.limits.creditsPerMonth.toLocaleString()})]}),(0,a.jsxs)("div",{className:"text-xs space-y-1 bg-slate-700/30 rounded p-2",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Subtitle download:"}),(0,a.jsx)("span",{children:"1 credit"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Language fetch (optional):"}),(0,a.jsx)("span",{children:"1 credit"})]}),(0,a.jsxs)("div",{className:"flex justify-between border-t border-slate-600 pt-1 font-medium text-gray-300",children:[(0,a.jsx)("span",{children:"English default:"}),(0,a.jsx)("span",{children:"1 credit only"})]})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 text-center space-y-1",children:[(0,a.jsxs)("div",{children:["~",N.limits.creditsPerMonth," subtitle downloads"]}),(0,a.jsx)("div",{className:"text-blue-400",children:"Credits valid for 6 months"})]})]})]})]}),(0,a.jsx)(m.wL,{children:(0,a.jsx)(x.$,{onClick:w,disabled:n||s,className:"w-full ".concat(N.popular?"bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700":"bg-slate-700 hover:bg-slate-600"," text-white"),children:n?"Processing...":s?"Current Plan":"Choose ".concat(N.name)})}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br ".concat((()=>{switch(t){case"starter":return"from-blue-500 to-cyan-500";case"pro":return"from-purple-500 to-pink-500";case"creator":return"from-yellow-500 to-orange-500";default:return"from-gray-500 to-gray-600"}})()," opacity-5 pointer-events-none")})]})})};var w=s(3215),k=s(2885);let A=e=>{let{onBack:t}=e,{user:s}=(0,f.A)(),{subscription:r}=(0,y.R)();return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-3 sm:px-6 lg:px-8 py-12",children:[t&&(0,a.jsx)(o.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:"mb-8",children:(0,a.jsxs)(x.$,{onClick:t,variant:"ghost",className:"text-gray-300 hover:text-white",children:[(0,a.jsx)(k.A,{className:"w-4 h-4 mr-2"}),"Back to Home"]})}),(0,a.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},className:"text-center mb-12",children:[(0,a.jsx)("h1",{className:"text-4xl sm:text-5xl md:text-6xl font-bold text-white mb-6",children:"Choose Your Credit Pack"}),(0,a.jsx)("p",{className:"text-xl text-gray-300 max-w-3xl mx-auto mb-8",children:"Get credits for YouTube subtitle extraction with our one-time purchase packs. Each subtitle download costs 1 credit. Credits are valid for 6 months."}),!s&&(0,a.jsx)("div",{className:"flex justify-center mb-8",children:(0,a.jsx)(w.A,{size:"lg",className:"bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700",children:"Sign in to Get Started"})})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12",children:Object.keys(j.D).map((e,t)=>(0,a.jsx)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.1*t},children:(0,a.jsx)(N,{tier:e,isCurrentPlan:(null==r?void 0:r.tier)===e&&(null==r?void 0:r.status)==="active"})},e))}),(0,a.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},className:"bg-slate-800/80 backdrop-blur-sm rounded-xl p-8 border border-slate-700",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-white mb-6 text-center",children:"Why Choose Our Premium Plans?"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("span",{className:"text-white font-bold text-xl",children:"⚡"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"Lightning Fast"}),(0,a.jsx)("p",{className:"text-gray-300 text-sm",children:"Extract subtitles in seconds with our optimized processing pipeline"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("span",{className:"text-white font-bold text-xl",children:"\uD83C\uDFAF"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"High Accuracy"}),(0,a.jsx)("p",{className:"text-gray-300 text-sm",children:"Advanced AI processing ensures the highest quality subtitle extraction"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("span",{className:"text-white font-bold text-xl",children:"\uD83D\uDD12"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"Secure & Private"}),(0,a.jsx)("p",{className:"text-gray-300 text-sm",children:"Your data is processed securely and never stored on our servers"})]})]})]}),(0,a.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},className:"mt-12 text-center",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-white mb-6",children:"Frequently Asked Questions"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 text-left",children:[(0,a.jsxs)("div",{className:"bg-slate-800/80 backdrop-blur-sm rounded-lg p-6 border border-slate-700",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"Can I buy more credits anytime?"}),(0,a.jsx)("p",{className:"text-gray-300 text-sm",children:"Yes! You can purchase additional credit packs at any time. Credits stack and are valid for 6 months."})]}),(0,a.jsxs)("div",{className:"bg-slate-800/80 backdrop-blur-sm rounded-lg p-6 border border-slate-700",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"What payment methods do you accept?"}),(0,a.jsx)("p",{className:"text-gray-300 text-sm",children:"We accept all major credit cards, debit cards, and digital wallets through Stripe."})]}),(0,a.jsxs)("div",{className:"bg-slate-800/80 backdrop-blur-sm rounded-lg p-6 border border-slate-700",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"How do credits work?"}),(0,a.jsx)("p",{className:"text-gray-300 text-sm",children:"Each subtitle download costs 1 credit. Credits are valid for 6 months from purchase date and don't expire monthly."})]}),(0,a.jsxs)("div",{className:"bg-slate-800/80 backdrop-blur-sm rounded-lg p-6 border border-slate-700",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"What happens if I run out of credits?"}),(0,a.jsx)("p",{className:"text-gray-300 text-sm",children:"You can purchase additional credit packs anytime. Credits from different purchases stack together."})]})]})]})]})})};function C(){let e=(0,l.useRouter)();return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(i(),{children:[(0,a.jsx)("title",{children:"Pricing - DownloadYTSubtitles"}),(0,a.jsx)("meta",{name:"description",content:"Choose your plan for DownloadYTSubtitles - YouTube subtitle extractor service."}),(0,a.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes"}),(0,a.jsx)("link",{rel:"icon",href:"/favicon.ico"})]}),(0,a.jsxs)("div",{className:"min-h-screen font-sans",children:[(0,a.jsx)(n.A,{currentView:"pricing",onNavigate:t=>{e.push("/".concat(t))},onFeedback:()=>{window.open("https://docs.google.com/forms/d/e/1FAIpQLSd1zn2jbwo7UKAkoRSPIV2RIxt2ZNi4VQYLqC8S0bV7CRZe_Q/viewform?usp=dialog","_blank")}}),(0,a.jsx)(A,{onBack:()=>{e.push("/")}}),(0,a.jsx)(c.A,{onTermsClick:()=>{e.push("/terms")},onPrivacyClick:()=>{e.push("/privacy")},onDisclaimerClick:()=>{e.push("/disclaimer")}})]})]})}},5101:(e,t,s)=>{"use strict";s.d(t,{E:()=>n});var a=s(7876);s(4232);var r=s(9518),i=s(8647);let l=(0,r.F)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function n(e){let{className:t,variant:s,...r}=e;return(0,a.jsx)("div",{className:(0,i.cn)(l({variant:s}),t),...r})}},6124:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(1713).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},6152:(e,t,s)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/pricing",function(){return s(4049)}])}},e=>{var t=t=>e(e.s=t);e.O(0,[587,893,817,701,636,593,792],()=>t(6152)),_N_E=e.O()}]);