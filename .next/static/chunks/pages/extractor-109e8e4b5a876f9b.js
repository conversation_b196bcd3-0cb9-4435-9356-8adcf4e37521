(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[440],{676:(e,t,a)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/extractor",function(){return a(6476)}])},5101:(e,t,a)=>{"use strict";a.d(t,{E:()=>i});var s=a(7876);a(4232);var l=a(9518),n=a(8647);let r=(0,l.F)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:a,...l}=e;return(0,s.jsx)("div",{className:(0,n.cn)(r({variant:a}),t),...l})}},6476:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>U});var s=a(7876),l=a(7328),n=a.n(l),r=a(9099),i=a(8606),c=a(4518),o=a(4232),d=a(1886),m=a(7212),x=a(6960),u=a(8647);let h=o.forwardRef((e,t)=>{let{className:a,type:l,...n}=e;return(0,s.jsx)("input",{type:l,className:(0,u.cn)("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",a),ref:t,...n})});h.displayName="Input";var p=a(8638),g=a(5101),b=a(9665),v=a(2885),w=a(4463),y=a(9239),f=a(7446),j=a(498),N=a(4822),k=a(6739),S=a(9812),A=a(9516),C=a(7897),F=a(2341),L=a(9969),P=a(5400),T=a(6171),V=a(3215),I=a(7685);let R=e=>{var t;let{onBack:a}=e,{user:l}=(0,L.A)(),{subscription:n,getRemainingCredits:r,getRemainingExtractions:i}=(0,P.R)(),[c,u]=(0,o.useState)(""),[R,U]=(0,o.useState)(null),[_,E]=(0,o.useState)([]),[M,z]=(0,o.useState)("en"),[D,Z]=(0,o.useState)(!1),[B,O]=(0,o.useState)(!1),[Y,$]=(0,o.useState)(0),[W,X]=(0,o.useState)("input"),[Q,q]=(0,o.useState)([]),[G,H]=(0,o.useState)(null),[J,K]=(0,o.useState)(!0),[ee,et]=(0,o.useState)("vtt"),[ea,es]=(0,o.useState)(""),[el,en]=(0,o.useState)(!1),er=(0,o.useMemo)(()=>ea.trim()?_.filter(e=>e.name.toLowerCase().includes(ea.toLowerCase())||e.code.toLowerCase().includes(ea.toLowerCase())):_,[_,ea]),ei=e=>{u(e),e.trim()?(Z(!0),setTimeout(()=>{let t=function(e){if(!e||"string"!=typeof e)return{isValid:!1,type:"invalid",url:e,error:"Please enter a valid URL"};let t=e.trim();for(let e of[/youtube\.com\/playlist\?list=([a-zA-Z0-9_-]+)/,/youtube\.com\/watch\?.*list=([a-zA-Z0-9_-]+)/])if(t.match(e))return{isValid:!1,type:"invalid",url:t,error:"Playlist support is coming soon. Please use a single video URL for now."};for(let e of[/(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/|youtube\.com\/v\/)([a-zA-Z0-9_-]{11})/,/youtube\.com\/watch\?.*v=([a-zA-Z0-9_-]{11})/]){let a=t.match(e);if(a){let e=a[1];if(e&&/^[a-zA-Z0-9_-]{11}$/.test(e))return{isValid:!0,type:"video",videoId:e,url:t}}}return t.includes("youtube.com")||t.includes("youtu.be")?{isValid:!1,type:"invalid",url:t,error:"Invalid YouTube URL format. Please check the URL and try again."}:{isValid:!1,type:"invalid",url:t,error:"Please enter a valid YouTube video URL"}}(e);U(t),Z(!1),!t.isValid&&e.trim()&&e.includes("youtube.com")&&(e.includes("watch?v=")||e.includes("youtu.be/"))&&I.Ay.error(t.error||"Invalid YouTube URL")},800)):(U(null),Z(!1))};(0,o.useLayoutEffect)(()=>{u("https://www.youtube.com/watch?v=VlRpgr0NOXI"),ei("https://www.youtube.com/watch?v=VlRpgr0NOXI")},[]);let ec=async()=>{if(null==R?void 0:R.isValid){if(!l)return void I.Ay.error("Please sign in to extract subtitles");O(!0),$(0),X("extracting");try{let e=setInterval(()=>{$(e=>Math.min(e+10,90))},200),{data:{session:t}}=await T.N.auth.getSession(),a=(null==t?void 0:t.access_token)?{Authorization:"Bearer ".concat(t.access_token)}:{},s=await fetch("/api/subtitles/download/".concat(R.videoId,"-en"),{headers:{"Content-Type":"application/json",...a}}),l=await s.json();clearInterval(e),$(100),s.ok?(q(l.subtitles||[]),H(l),z("en"),X("preview"),I.Ay.success("English subtitles extracted successfully!")):(I.Ay.error(l.error||"Failed to extract English subtitles"),X("input"))}catch(e){I.Ay.error("Network error. Please try again."),console.error(e),X("input")}finally{O(!1),$(0)}}},eo=async()=>{if(null==R?void 0:R.isValid){if(!l)return void I.Ay.error("Please sign in to get available languages");O(!0),$(0);try{let t=setInterval(()=>{$(e=>Math.min(e+10,90))},200),{data:{session:a}}=await T.N.auth.getSession(),s=(null==a?void 0:a.access_token)?{Authorization:"Bearer ".concat(a.access_token)}:{},l=await fetch("/api/subtitles/languages/".concat(R.videoId),{headers:{"Content-Type":"application/json",...s}}),n=await l.json();if(clearInterval(t),$(100),l.ok){var e;E(n.languages||[]),H(n),X("languages"),I.Ay.success("Found ".concat((null==(e=n.languages)?void 0:e.length)||0," available languages!"))}else I.Ay.error(n.error||"Failed to get available languages"),X("input")}catch(e){I.Ay.error("Network error. Please try again"),X("input")}finally{O(!1),$(0)}}},ed=async()=>{if((null==R?void 0:R.isValid)&&M){if(!l)return void I.Ay.error("Please sign in to extract subtitles");O(!0),$(0),X("extracting");try{let e=setInterval(()=>{$(e=>Math.min(e+10,90))},200),{data:{session:t}}=await T.N.auth.getSession(),a=(null==t?void 0:t.access_token)?{Authorization:"Bearer ".concat(t.access_token)}:{},s=await fetch("/api/subtitles/download/".concat(R.videoId,"-").concat(M),{headers:{"Content-Type":"application/json",...a}}),l=await s.json();clearInterval(e),$(100),s.ok?(q(l.subtitles||[]),H(l),X("preview"),I.Ay.success("Subtitles extracted successfully!")):(I.Ay.error(l.error||"Failed to extract subtitles"),X("languages"))}catch(e){I.Ay.error("Network error. Please try again."),console.error(e),X("languages")}finally{O(!1),$(0)}}},em=async()=>{if((null==R?void 0:R.isValid)&&_.length){if(!l)return void I.Ay.error("Please sign in to download all languages");O(!0),$(0),X("extracting");try{let t=setInterval(()=>{$(e=>Math.min(e+10,90))},300),{data:{session:a}}=await T.N.auth.getSession(),s=(null==a?void 0:a.access_token)?{Authorization:"Bearer ".concat(a.access_token)}:{},l=await fetch("/api/subtitles/download-all/".concat(R.videoId),{headers:{"Content-Type":"application/json",...s}}),n=await l.json();if(clearInterval(t),$(100),l.ok){var e;let t=new Blob([n.zipData],{type:"application/zip"}),a=URL.createObjectURL(t),s=document.createElement("a");s.href=a,s.download="".concat((null==G||null==(e=G.title)?void 0:e.replace(/[^a-z0-9]/gi,"_").toLowerCase())||"subtitles","_all_languages.zip"),document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL(a),I.Ay.success("All ".concat(_.length," languages downloaded successfully!")),X("languages")}else I.Ay.error(n.error||"Failed to download all languages"),X("languages")}catch(e){I.Ay.error("Network error. Please try again."),console.error(e),X("languages")}finally{O(!1),$(0)}}},ex=e=>{var t;if(!Q.length)return;let a="",s="".concat((null==G||null==(t=G.title)?void 0:t.replace(/[^a-z0-9]/gi,"_").toLowerCase())||"subtitles",".").concat(e);switch(e){case"vtt":a="WEBVTT\n\n",Q.forEach((e,t)=>{let s=e=>{let t=String(null!=e?e:"");if(t.includes(":")&&3===t.split(":").length)return t;let a=parseFloat(t);if(isNaN(a))return"00:00:00.000";let s=Math.floor(a/3600),l=Math.floor(a%3600/60),n=(a%60).toFixed(3);return"".concat(String(s).padStart(2,"0"),":").concat(String(l).padStart(2,"0"),":").concat(n.padStart(6,"0"))};console.log(e);let l=s(e.start),n=s(e.end);a+="".concat(t+1,"\n").concat(l," --\x3e ").concat(n,"\n").concat(e.text,"\n\n")});break;case"txt":a="Title: ".concat((null==G?void 0:G.title)||"Unknown","\n")+"Video ID: ".concat((null==R?void 0:R.videoId)||"Unknown","\n")+"Channel: ".concat((null==G?void 0:G.uploader)||(null==G?void 0:G.channel)||"Unknown","\n\n")+"---\n\n",Q.forEach(e=>{let t=e=>{let t=parseFloat(e),a=Math.floor(t/60),s=Math.floor(t%60);return"".concat(a,":").concat(s.toString().padStart(2,"0"))},s=t(e.start),l=t(e.end);a+="[".concat(s," - ").concat(l,"] ").concat(e.text,"\n\n")});break;case"json":a=JSON.stringify({metadata:{title:(null==G?void 0:G.title)||"YouTube Video",channel:(null==G?void 0:G.channel)||"Unknown Channel",videoId:(null==R?void 0:R.videoId)||"Unknown",language:M,duration:(null==G?void 0:G.duration)||null,extractedAt:new Date().toISOString(),totalSubtitles:Q.length},subtitles:Q.map((e,t)=>({index:t+1,start:e.start,end:e.end,duration:e.end-e.start,text:e.text,startTime:(()=>{let t=parseFloat(e.start),a=Math.floor(t/3600),s=Math.floor(t%3600/60),l=(t%60).toFixed(3);return"".concat(String(a).padStart(2,"0"),":").concat(String(s).padStart(2,"0"),":").concat(l.padStart(6,"0"))})(),endTime:(()=>{let t=parseFloat(e.end),a=Math.floor(t/3600),s=Math.floor(t%3600/60),l=(t%60).toFixed(3);return"".concat(String(a).padStart(2,"0"),":").concat(String(s).padStart(2,"0"),":").concat(l.padStart(6,"0"))})()}))},null,2)}let l=new Blob([a],{type:"json"===e?"application/json":"text/plain"}),n=URL.createObjectURL(l),r=document.createElement("a");r.href=n,r.download=s,document.body.appendChild(r),r.click(),document.body.removeChild(r),URL.revokeObjectURL(n),I.Ay.success("Downloaded ".concat(s))};return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 py-4 sm:py-8",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto px-3 sm:px-6 lg:px-8",children:[(0,s.jsxs)(d.P.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"flex items-center justify-between mb-6 sm:mb-8",children:[(0,s.jsxs)(x.$,{variant:"ghost",onClick:a,className:"text-white hover:text-purple-400 text-sm sm:text-base p-2 sm:p-3",children:[(0,s.jsx)(v.A,{className:"w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2"}),(0,s.jsx)("span",{className:"hidden xs:inline",children:"Back to Home"}),(0,s.jsx)("span",{className:"xs:hidden",children:"Back"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-1 sm:space-x-2",children:[(0,s.jsx)("div",{className:"w-2 h-2 sm:w-3 sm:h-3 rounded-full ".concat("input"===W?"bg-purple-500":"languages"===W||"extracting"===W||"preview"===W?"bg-green-500":"bg-gray-600")}),(0,s.jsx)("div",{className:"w-2 h-2 sm:w-3 sm:h-3 rounded-full ".concat("languages"===W?"bg-purple-500":"extracting"===W||"preview"===W?"bg-green-500":"bg-gray-600")}),(0,s.jsx)("div",{className:"w-2 h-2 sm:w-3 sm:h-3 rounded-full ".concat("extracting"===W?"bg-purple-500":"preview"===W?"bg-green-500":"bg-gray-600")}),(0,s.jsx)("div",{className:"w-2 h-2 sm:w-3 sm:h-3 rounded-full ".concat("preview"===W?"bg-purple-500":"bg-gray-600")})]})]}),l&&n&&(0,s.jsx)(d.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mb-6",children:(0,s.jsx)(p.Zp,{className:"bg-slate-800/60 backdrop-blur-sm border-slate-700",children:(0,s.jsx)(p.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(w.A,{className:"w-4 h-4 text-yellow-500"}),(0,s.jsxs)("span",{className:"text-white font-medium capitalize",children:[n.tier," Plan"]})]}),(0,s.jsxs)("div",{className:"text-sm text-gray-300",children:[r()," credits • ~",i()," extractions remaining"]})]})})})}),!l&&(0,s.jsx)(d.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mb-6",children:(0,s.jsx)(p.Zp,{className:"bg-slate-800/60 backdrop-blur-sm border-slate-700",children:(0,s.jsx)(p.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(y.A,{className:"w-4 h-4 text-orange-500"}),(0,s.jsx)("span",{className:"text-white font-medium",children:"Sign in required"})]}),(0,s.jsx)(V.A,{size:"sm",variant:"outline"})]})})})}),(0,s.jsxs)(m.N,{mode:"wait",children:["input"===W&&(0,s.jsx)(d.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},exit:{opacity:0,x:20},transition:{duration:.3},children:(0,s.jsxs)(p.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700",children:[(0,s.jsxs)(p.aR,{className:"pb-4 sm:pb-6",children:[(0,s.jsx)(p.ZB,{className:"text-white text-xl sm:text-2xl",children:"Enter YouTube URL"}),(0,s.jsx)("p",{className:"text-gray-300 text-sm sm:text-base",children:"Paste a YouTube video URL to get started"})]}),(0,s.jsxs)(p.Wu,{className:"space-y-4 sm:space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(h,{placeholder:"https://www.youtube.com/watch?v=...",value:c,onChange:e=>ei(e.target.value),className:"bg-slate-700 border-slate-600 text-white placeholder-gray-400 text-sm sm:text-base"}),D&&(0,s.jsxs)("div",{className:"flex items-center text-gray-400 text-xs sm:text-sm",children:[(0,s.jsx)(f.A,{className:"w-3 h-3 sm:w-4 sm:h-4 mr-2 animate-spin"}),"Validating URL..."]}),R&&(0,s.jsx)(d.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"flex items-center space-x-2 flex-wrap",children:R.isValid?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(j.A,{className:"w-4 h-4 sm:w-5 sm:h-5 text-green-400"}),(0,s.jsx)("span",{className:"text-green-400 text-sm sm:text-base",children:"Valid YouTube Video"}),(0,s.jsxs)(g.E,{variant:"secondary",className:"bg-purple-600 text-white text-xs",children:[(0,s.jsx)(N.A,{className:"w-3 h-3 mr-1"})," Video"]})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(k.A,{className:"w-4 h-4 sm:w-5 sm:h-5 text-red-400"}),(0,s.jsx)("span",{className:"text-red-400 text-sm sm:text-base",children:R.error})]})})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(x.$,{onClick:ec,disabled:!(null==R?void 0:R.isValid)||B,className:"w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 py-3 sm:py-4 text-sm sm:text-base",children:B?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(f.A,{className:"w-3 h-3 sm:w-4 sm:h-4 mr-2 animate-spin"}),(0,s.jsx)("span",{className:"hidden xs:inline",children:"Extracting..."}),(0,s.jsx)("span",{className:"xs:hidden",children:"Loading..."})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(S.A,{className:"w-3 h-3 sm:w-4 sm:h-4 mr-2"}),"Extract English Subtitles (1 Credit)"]})}),(0,s.jsx)(x.$,{onClick:eo,disabled:!(null==R?void 0:R.isValid)||B,className:"w-full bg-slate-700 hover:bg-slate-600 py-3 sm:py-4 text-sm sm:text-base",children:B?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(f.A,{className:"w-3 h-3 sm:w-4 sm:h-4 mr-2 animate-spin"}),(0,s.jsx)("span",{className:"hidden xs:inline",children:"Getting Languages..."}),(0,s.jsx)("span",{className:"xs:hidden",children:"Loading..."})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(A.A,{className:"w-3 h-3 sm:w-4 sm:h-4 mr-2"}),"Browse All Languages (+1 Credit)"]})})]})]})]})},"input"),"languages"===W&&(0,s.jsx)(d.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},exit:{opacity:0,x:20},transition:{duration:.3},children:(0,s.jsxs)(p.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700",children:[(0,s.jsxs)(p.aR,{className:"pb-4 sm:pb-6",children:[(0,s.jsx)(p.ZB,{className:"text-white text-xl sm:text-2xl",children:"Select Language"}),(0,s.jsx)("p",{className:"text-gray-300 text-sm sm:text-base",children:"Choose the subtitle language you want to extract"})]}),(0,s.jsxs)(p.Wu,{className:"space-y-4 sm:space-y-6",children:[G&&(0,s.jsxs)("div",{className:"flex items-center space-x-3 sm:space-x-4 p-3 sm:p-4 bg-slate-700/50 rounded-lg",children:[(0,s.jsx)("img",{src:G.thumbnail,alt:G.title,className:"w-16 h-12 sm:w-20 sm:h-15 object-cover rounded"}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("h3",{className:"text-white font-semibold text-sm sm:text-base truncate",children:G.title}),G.channel&&(0,s.jsxs)("p",{className:"text-blue-400 text-xs sm:text-sm truncate",children:["\uD83D\uDCFA ",G.channel]}),(0,s.jsxs)("p",{className:"text-gray-400 text-xs sm:text-sm",children:["Video • ",_.length," languages available"]}),G.duration&&(0,s.jsxs)("p",{className:"text-gray-400 text-xs",children:["⏱️ ",Math.floor(G.duration/60),":",String(G.duration%60).padStart(2,"0")]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{className:"text-white font-medium text-sm sm:text-base",children:"Available Languages:"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(A.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-3 h-3 sm:w-4 sm:h-4"}),(0,s.jsx)(h,{placeholder:"Search languages...",value:ea,onChange:e=>es(e.target.value),className:"bg-slate-700 border-slate-600 text-white placeholder-gray-400 pl-9 sm:pl-10 text-sm sm:text-base"})]}),(0,s.jsx)("div",{className:"max-h-48 sm:max-h-64 overflow-y-auto bg-slate-700 rounded-lg border border-slate-600",children:0===er.length?(0,s.jsx)("div",{className:"p-3 sm:p-4 text-center text-gray-400 text-sm sm:text-base",children:"No languages found"}):er.map(e=>(0,s.jsx)("div",{onClick:()=>z(e.code),className:"p-3 cursor-pointer hover:bg-slate-600 transition-colors ".concat(M===e.code?"bg-purple-600":""),children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-white text-sm sm:text-base",children:e.name}),(0,s.jsxs)("div",{className:"flex items-center space-x-1 sm:space-x-2",children:[e.isAutoGenerated&&(0,s.jsx)(g.E,{variant:"secondary",className:"bg-orange-600 text-white text-xs",children:"Auto"}),M===e.code&&(0,s.jsx)(j.A,{className:"w-3 h-3 sm:w-4 sm:h-4 text-white"})]})]})},e.code))})]}),_.length>1&&(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsxs)(x.$,{onClick:em,disabled:B,className:"w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 py-2 sm:py-3 text-sm sm:text-base",children:[(0,s.jsx)(S.A,{className:"w-3 h-3 sm:w-4 sm:h-4 mr-2"}),"Download All ",_.length," Languages (1 Credit)"]}),(0,s.jsx)("p",{className:"text-xs text-gray-400 text-center mt-2",children:"Downloads all available languages as a ZIP file"})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3",children:[(0,s.jsxs)(x.$,{variant:"outline",onClick:()=>X("input"),className:"flex-1 border-slate-600 text-black hover:bg-slate-700 hover:text-white py-2 sm:py-3 text-sm sm:text-base",children:[(0,s.jsx)(v.A,{className:"w-3 h-3 sm:w-4 sm:h-4 mr-2"}),"Back"]}),(0,s.jsxs)(x.$,{onClick:ed,disabled:!M,className:"flex-1 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 py-2 sm:py-3 text-sm sm:text-base",children:[(0,s.jsx)(S.A,{className:"w-3 h-3 sm:w-4 sm:h-4 mr-2"}),"Extract Selected Language"]})]})]})]})},"languages"),"extracting"===W&&(0,s.jsx)(d.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},exit:{opacity:0,x:20},transition:{duration:.3},children:(0,s.jsxs)(p.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700",children:[(0,s.jsxs)(p.aR,{className:"pb-4 sm:pb-6",children:[(0,s.jsx)(p.ZB,{className:"text-white text-xl sm:text-2xl",children:"Extracting Subtitles"}),(0,s.jsx)("p",{className:"text-gray-300 text-sm sm:text-base",children:"Please wait while we extract and process the subtitles"})]}),(0,s.jsxs)(p.Wu,{className:"space-y-4 sm:space-y-6",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(d.P.div,{animate:{rotate:360},transition:{duration:2,repeat:1/0,ease:"linear"},className:"inline-block",children:(0,s.jsx)(f.A,{className:"w-12 h-12 sm:w-16 sm:h-16 text-purple-500"})}),(0,s.jsx)("p",{className:"text-white mt-3 sm:mt-4 text-base sm:text-lg",children:"Processing..."})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex justify-between text-xs sm:text-sm",children:[(0,s.jsx)("span",{className:"text-gray-300",children:"Progress"}),(0,s.jsxs)("span",{className:"text-purple-400",children:[Y,"%"]})]}),(0,s.jsx)(b.k,{value:Y,className:"h-2"})]}),(0,s.jsxs)("div",{className:"text-center text-gray-400 text-xs sm:text-sm px-2",children:[Y<30&&"Fetching video information...",Y>=30&&Y<60&&"Downloading subtitle data...",Y>=60&&Y<90&&"Processing and cleaning subtitles...",Y>=90&&"Almost done..."]})]})]})},"extracting"),"preview"===W&&(0,s.jsx)(d.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},exit:{opacity:0,x:20},transition:{duration:.3},children:(0,s.jsxs)(p.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700",children:[(0,s.jsxs)(p.aR,{className:"pb-4 sm:pb-6",children:[(0,s.jsx)(p.ZB,{className:"text-white text-xl sm:text-2xl",children:"Subtitles Ready!"}),(0,s.jsx)("p",{className:"text-gray-300 text-sm sm:text-base",children:"Preview and download your extracted subtitles"})]}),(0,s.jsxs)(p.Wu,{className:"space-y-4 sm:space-y-6",children:[G&&(0,s.jsxs)("div",{className:"flex items-center space-x-3 sm:space-x-4 p-3 sm:p-4 bg-slate-700/50 rounded-lg",children:[(0,s.jsx)("img",{src:G.thumbnail,alt:G.title,className:"w-16 h-12 sm:w-20 sm:h-15 object-cover rounded"}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("h3",{className:"text-white font-semibold text-sm sm:text-base truncate",children:G.title}),G.channel&&(0,s.jsxs)("p",{className:"text-blue-400 text-xs sm:text-sm truncate",children:["\uD83D\uDCFA ",G.channel]}),(0,s.jsxs)("p",{className:"text-gray-400 text-xs sm:text-sm",children:[Q.length," subtitle entries • ",(null==(t=_.find(e=>e.code===M))?void 0:t.name)||"English"]}),G.duration&&(0,s.jsxs)("p",{className:"text-gray-400 text-xs",children:["⏱️ ",Math.floor(G.duration/60),":",String(G.duration%60).padStart(2,"0")]})]}),(0,s.jsx)(j.A,{className:"w-6 h-6 sm:w-8 sm:h-8 text-green-400 flex-shrink-0"})]}),(0,s.jsxs)("div",{className:"space-y-3 sm:space-y-4",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0",children:[(0,s.jsx)("label",{className:"text-white font-medium text-sm sm:text-base",children:"Select Format:"}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:["vtt","txt","json"].map(e=>(0,s.jsx)(x.$,{variant:ee===e?"default":"outline",size:"sm",onClick:()=>et(e),className:"text-xs sm:text-sm px-3 sm:px-4 ".concat(ee===e?"bg-purple-600 hover:bg-purple-700":"border-slate-600 text-black hover:bg-slate-700 hover:text-white"),children:e.toUpperCase()},e))})]}),(0,s.jsxs)(x.$,{onClick:()=>ex(ee),className:"w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 py-3 sm:py-4 text-sm sm:text-base",children:[(0,s.jsx)(S.A,{className:"w-3 h-3 sm:w-4 sm:h-4 mr-2"}),"Download ",ee.toUpperCase()," Format"]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0",children:[(0,s.jsxs)("label",{className:"text-white font-medium text-sm sm:text-base",children:["Preview (",ee.toUpperCase(),"):"]}),(0,s.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>K(!J),className:"border-slate-600 text-black hover:bg-slate-700 hover:text-white text-xs sm:text-sm self-start sm:self-auto",children:J?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(C.A,{className:"w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2"})," Hide"]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(F.A,{className:"w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2"})," Show"]})})]}),J&&(0,s.jsx)(d.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"max-h-64 sm:max-h-96 overflow-y-auto bg-slate-900 rounded-lg p-3 sm:p-4 border border-slate-600",children:(0,s.jsx)("pre",{className:"text-xs sm:text-sm text-white whitespace-pre-wrap font-mono",children:(e=>{if(!Q.length)return"";switch(e){case"vtt":let t="WEBVTT\n\n";return Q.forEach((e,a)=>{let s=e=>{let t=String(null!=e?e:"");if(t.includes(":")&&3===t.split(":").length)return t;let a=parseFloat(t);if(isNaN(a))return"00:00:00.000";let s=Math.floor(a/3600),l=Math.floor(a%3600/60),n=(a%60).toFixed(3);return"".concat(String(s).padStart(2,"0"),":").concat(String(l).padStart(2,"0"),":").concat(n.padStart(6,"0"))},l=s(e.start),n=s(e.end);t+="".concat(a+1,"\n").concat(l," --\x3e ").concat(n,"\n").concat(e.text,"\n\n")}),t;case"txt":let a="Title: ".concat((null==G?void 0:G.title)||"Unknown","\n");return a+="Video ID: ".concat((null==R?void 0:R.videoId)||"Unknown","\n"),a+="Channel: ".concat((null==G?void 0:G.uploader)||(null==G?void 0:G.channel)||"Unknown","\n\n"),a+="---\n\n",Q.forEach(e=>{let t=e=>{let t=parseFloat(e),a=Math.floor(t/60),s=Math.floor(t%60);return"".concat(a,":").concat(s.toString().padStart(2,"0"))},s=t(e.start),l=t(e.end);a+="[".concat(s," - ").concat(l,"] ").concat(e.text,"\n\n")}),a;case"json":let s=JSON.stringify({metadata:{title:(null==G?void 0:G.title)||"YouTube Video",channel:(null==G?void 0:G.channel)||"Unknown Channel",videoId:(null==R?void 0:R.videoId)||"Unknown",language:M,duration:(null==G?void 0:G.duration)||null,extractedAt:new Date().toISOString(),totalSubtitles:Q.length},subtitles:Q.slice(0,5).map((e,t)=>({index:t+1,start:e.start,end:e.end,duration:e.end-e.start,text:e.text,startTime:(()=>{let t=parseFloat(e.start),a=Math.floor(t/3600),s=Math.floor(t%3600/60),l=(t%60).toFixed(3);return"".concat(String(a).padStart(2,"0"),":").concat(String(s).padStart(2,"0"),":").concat(l.padStart(6,"0"))})(),endTime:(()=>{let t=parseFloat(e.end),a=Math.floor(t/3600),s=Math.floor(t%3600/60),l=(t%60).toFixed(3);return"".concat(String(a).padStart(2,"0"),":").concat(String(s).padStart(2,"0"),":").concat(l.padStart(6,"0"))})()}))},null,2);return Q.length>5&&(s+="\n\n... and ".concat(Q.length-5," more subtitle entries")),s;default:return""}})(ee)})})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3",children:[(0,s.jsx)(x.$,{variant:"outline",onClick:()=>{u(""),U(null),E([]),z("en"),X("input"),q([]),H(null),K(!1),et("vtt")},className:"flex-1 border-slate-600 text-black hover:bg-slate-700 hover:text-white py-2 sm:py-3 text-sm sm:text-base",children:"Extract Another"}),(0,s.jsx)(x.$,{onClick:()=>X("input"),className:"flex-1 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 py-2 sm:py-3 text-sm sm:text-base",children:"Start Over"})]})]})]})},"preview")]})]})})};function U(){let e=(0,r.useRouter)();return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(n(),{children:[(0,s.jsx)("title",{children:"Extract YouTube Subtitles - DownloadYTSubtitles"}),(0,s.jsx)("meta",{name:"description",content:"Extract and download YouTube subtitles in VTT and TXT formats. Fast, free, and easy to use."}),(0,s.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes"}),(0,s.jsx)("link",{rel:"icon",href:"/favicon.ico"})]}),(0,s.jsxs)("div",{className:"min-h-screen font-sans",children:[(0,s.jsx)(i.A,{currentView:"extractor",onNavigate:t=>{e.push("/".concat(t))},onFeedback:()=>{window.open("https://docs.google.com/forms/d/e/1FAIpQLSd1zn2jbwo7UKAkoRSPIV2RIxt2ZNi4VQYLqC8S0bV7CRZe_Q/viewform?usp=dialog","_blank")}}),(0,s.jsx)(R,{onBack:()=>{e.push("/")}}),(0,s.jsx)(c.A,{onTermsClick:()=>{e.push("/terms")},onPrivacyClick:()=>{e.push("/privacy")},onDisclaimerClick:()=>{e.push("/disclaimer")}})]})]})}},9665:(e,t,a)=>{"use strict";a.d(t,{k:()=>i});var s=a(7876),l=a(4232),n=a(4972),r=a(8647);let i=l.forwardRef((e,t)=>{let{className:a,value:l,...i}=e;return(0,s.jsx)(n.bL,{ref:t,className:(0,r.cn)("relative h-2 w-full overflow-hidden rounded-full bg-primary/20",a),...i,children:(0,s.jsx)(n.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(l||0),"%)")}})})});i.displayName=n.bL.displayName}},e=>{var t=t=>e(e.s=t);e.O(0,[587,893,817,612,701,636,593,792],()=>t(676)),_N_E=e.O()}]);