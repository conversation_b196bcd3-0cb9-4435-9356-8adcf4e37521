(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[332],{3817:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>S});var a=s(7876),i=s(7328),l=s.n(i),o=s(9099),n=s(8606),r=s(4518),c=s(1886),d=s(4822),m=s(769),u=s(2467),x=s(9812),p=s(7297),h=s(7396),b=s(6124),g=s(4463),w=s(4622),y=s(6960),f=s(8638),v=s(4564),j=s(9516),N=s(2341),T=s(498);let Y=()=>{let e=[{step:1,title:"Paste YouTube URL",description:"Copy and paste any YouTube video URL into the input field. Our system will automatically validate the URL and detect if it's a valid YouTube video.",icon:(0,a.jsx)(v.A,{className:"w-8 h-8"}),image:"/images/step1-url-input.png",tips:["Works with any YouTube video URL format","Automatic URL validation with instant feedback","Supports both youtube.com and youtu.be links"]},{step:2,title:"Select Language",description:"Browse through available subtitle languages with our searchable interface. English is automatically selected when available.",icon:(0,a.jsx)(j.A,{className:"w-8 h-8"}),image:"/images/step2-language-select.png",tips:["Search through 70+ supported languages","Auto-generated subtitles clearly marked","English automatically selected as default"]},{step:3,title:"Extract & Process",description:"Watch the real-time progress as we extract and clean the subtitles. Our advanced processing ensures perfect formatting.",icon:(0,a.jsx)(x.A,{className:"w-8 h-8"}),image:"/images/step3-extraction.png",tips:["Real-time progress tracking","Advanced subtitle cleaning algorithms","Automatic formatting optimization"]},{step:4,title:"Preview & Download",description:"Preview your subtitles in VTT, SRT, or TXT format before downloading. See exactly what you'll get with full content preview.",icon:(0,a.jsx)(N.A,{className:"w-8 h-8"}),image:"/images/step4-preview-download.png",tips:["Preview in multiple formats","Full content scrollable preview","One-click download in preferred format"]}];return(0,a.jsx)("section",{id:"how-to-use",className:"py-12 sm:py-20 bg-slate-800/30",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-3 sm:px-6 lg:px-8",children:[(0,a.jsxs)(c.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-12 sm:mb-16",children:[(0,a.jsx)("h2",{className:"text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-3 sm:mb-4",children:"How to Use"}),(0,a.jsx)("p",{className:"text-lg sm:text-xl text-gray-300 max-w-2xl mx-auto px-2",children:"Extract YouTube subtitles in 4 simple steps. No registration required, completely free to use."})]}),(0,a.jsx)("div",{className:"space-y-12 sm:space-y-16",children:e.map((t,s)=>(0,a.jsxs)(c.P.div,{initial:{opacity:0,y:40},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.2*s},viewport:{once:!0},className:"flex flex-col ".concat(s%2==0?"lg:flex-row":"lg:flex-row-reverse"," items-center gap-8 sm:gap-12"),children:[(0,a.jsxs)("div",{className:"flex-1 space-y-4 sm:space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 sm:space-x-4",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full",children:(0,a.jsx)("span",{className:"text-lg sm:text-2xl font-bold text-white",children:t.step})}),(0,a.jsx)("div",{className:"text-purple-400",children:t.icon})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-2xl sm:text-3xl font-bold text-white mb-3 sm:mb-4",children:t.title}),(0,a.jsx)("p",{className:"text-base sm:text-lg text-gray-300 leading-relaxed",children:t.description})]}),(0,a.jsxs)("div",{className:"space-y-2 sm:space-y-3",children:[(0,a.jsx)("h4",{className:"text-base sm:text-lg font-semibold text-white",children:"Key Features:"}),(0,a.jsx)("ul",{className:"space-y-2",children:t.tips.map((e,t)=>(0,a.jsxs)("li",{className:"flex items-start space-x-2 sm:space-x-3",children:[(0,a.jsx)(T.A,{className:"w-4 h-4 sm:w-5 sm:h-5 text-green-400 flex-shrink-0 mt-0.5"}),(0,a.jsx)("span",{className:"text-sm sm:text-base text-gray-300",children:e})]},t))})]}),s<e.length-1&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-purple-400",children:[(0,a.jsx)("span",{className:"text-xs sm:text-sm font-medium",children:"Next Step"}),(0,a.jsx)(w.A,{className:"w-3 h-3 sm:w-4 sm:h-4"})]})]}),(0,a.jsx)("div",{className:"flex-1 w-full",children:(0,a.jsx)(f.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700 overflow-hidden",children:(0,a.jsx)(f.Wu,{className:"p-0",children:(0,a.jsx)("div",{className:"aspect-video bg-gradient-to-br from-slate-700 to-slate-800 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center p-4",children:[(0,a.jsx)("div",{className:"w-16 h-16 sm:w-24 sm:h-24 mx-auto mb-3 sm:mb-4 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center",children:t.icon}),(0,a.jsxs)("h4",{className:"text-white font-semibold mb-2 text-sm sm:text-base",children:["Step ",t.step," Preview"]}),(0,a.jsx)("p",{className:"text-gray-400 text-xs sm:text-sm",children:"Screenshot coming soon"})]})})})})})]},t.step))})]})})};var k=s(6093),A=s(427);let P=e=>{let{onGetStarted:t,onNavigateToPricing:s}=e,i=[{icon:(0,a.jsx)(d.A,{className:"w-8 h-8"}),title:"YouTube Video Support",description:"Download subtitles from any YouTube video with available captions or auto-generated transcripts."},{icon:(0,a.jsx)(m.A,{className:"w-8 h-8"}),title:"70+ Languages Supported",description:"Extract YouTube captions in multiple languages including English, Spanish, French, German, and more."},{icon:(0,a.jsx)(u.A,{className:"w-8 h-8"}),title:"Auto-Generated Captions",description:"Advanced processing of YouTube's AI-generated subtitles with automatic cleaning and formatting."},{icon:(0,a.jsx)(x.A,{className:"w-8 h-8"}),title:"VTT & TXT Formats",description:"Download YouTube subtitles in VTT format for video players and web, or TXT with metadata for reading."},{icon:(0,a.jsx)(p.A,{className:"w-8 h-8"}),title:"Privacy Protected",description:"No data stored on our servers. YouTube subtitle extraction happens in real-time without tracking."},{icon:(0,a.jsx)(h.A,{className:"w-8 h-8"}),title:"Professional Plans",description:"Choose from flexible pricing plans designed for individuals, teams, and businesses of all sizes."}];return(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900",children:[(0,a.jsxs)("section",{className:"relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-purple-800/20 to-pink-800/20"}),(0,a.jsx)("div",{className:"relative max-w-7xl mx-auto px-3 sm:px-6 lg:px-8 pt-12 sm:pt-20 pb-12 sm:pb-16",children:(0,a.jsxs)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},className:"text-center",children:[(0,a.jsxs)(c.P.h1,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},className:"text-3xl sm:text-5xl md:text-7xl font-bold text-white mb-4 sm:mb-6 leading-tight",children:["Download YouTube",(0,a.jsxs)("span",{className:"bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent",children:[" ","Subtitles"]})]}),(0,a.jsx)(c.P.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},className:"text-base sm:text-xl md:text-2xl text-gray-300 mb-6 sm:mb-8 max-w-3xl mx-auto px-2",children:"Professional YouTube subtitle downloader and transcript extractor. Get YT captions in VTT and TXT formats. Export YouTube transcripts from auto-generated and manual subtitles in 70+ languages."}),(0,a.jsxs)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},className:"flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center",children:[(0,a.jsxs)(y.$,{onClick:t,size:"lg",className:"w-full sm:w-auto bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300",children:[(0,a.jsx)(d.A,{className:"w-4 h-4 sm:w-5 sm:h-5 mr-2"}),"Get Started"]}),(0,a.jsx)(y.$,{variant:"outline",size:"lg",className:"w-full sm:w-auto border-purple-400 text-purple-400 hover:bg-purple-400 hover:text-white px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg font-semibold rounded-full transition-all duration-300",onClick:()=>{var e;null==(e=document.getElementById("features"))||e.scrollIntoView({behavior:"smooth"})},children:"Learn More"})]})]})})]}),(0,a.jsx)("section",{id:"features",className:"py-12 sm:py-20 bg-slate-800/50",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-3 sm:px-6 lg:px-8",children:[(0,a.jsxs)(c.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-12 sm:mb-16",children:[(0,a.jsx)("h2",{className:"text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-3 sm:mb-4",children:"Powerful Features"}),(0,a.jsx)("p",{className:"text-lg sm:text-xl text-gray-300 max-w-2xl mx-auto px-2",children:"Everything you need to extract and process YouTube subtitles professionally"})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-8",children:i.map((e,t)=>(0,a.jsxs)(c.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.1*t},viewport:{once:!0},whileHover:{scale:1.02},className:"bg-slate-800/80 backdrop-blur-sm rounded-xl p-4 sm:p-6 border border-slate-700 hover:border-purple-500 transition-all duration-300",children:[(0,a.jsx)("div",{className:"text-purple-400 mb-3 sm:mb-4",children:e.icon}),(0,a.jsx)("h3",{className:"text-lg sm:text-xl font-semibold text-white mb-2",children:e.title}),(0,a.jsx)("p",{className:"text-sm sm:text-base text-gray-300",children:e.description})]},t))})]})}),(0,a.jsx)("section",{className:"py-12 sm:py-20",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-3 sm:px-6 lg:px-8",children:[(0,a.jsxs)(c.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-12 sm:mb-16",children:[(0,a.jsx)("h2",{className:"text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-3 sm:mb-4",children:"Perfect For Everyone"}),(0,a.jsx)("p",{className:"text-lg sm:text-xl text-gray-300 max-w-2xl mx-auto px-2",children:"From content creators to researchers, our tool serves diverse needs"})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-8",children:[{title:"Content Creators",description:"Extract subtitles for video editing, translation, or accessibility compliance.",gradient:"from-purple-500 to-pink-500"},{title:"Students & Researchers",description:"Get transcripts from educational videos for note-taking and research.",gradient:"from-blue-500 to-cyan-500"},{title:"Language Learners",description:"Study foreign languages with accurate subtitles and translations.",gradient:"from-green-500 to-emerald-500"},{title:"Accessibility Teams",description:"Create accessible content with properly formatted subtitle files.",gradient:"from-orange-500 to-red-500"}].map((e,t)=>(0,a.jsxs)(c.P.div,{initial:{opacity:0,x:t%2==0?-20:20},whileInView:{opacity:1,x:0},transition:{duration:.8,delay:.2*t},viewport:{once:!0},className:"relative overflow-hidden rounded-xl bg-slate-800/80 backdrop-blur-sm border border-slate-700 p-4 sm:p-8 hover:border-purple-500 transition-all duration-300",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r ".concat(e.gradient," opacity-10")}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("h3",{className:"text-xl sm:text-2xl font-bold text-white mb-3 sm:mb-4",children:e.title}),(0,a.jsx)("p",{className:"text-gray-300 text-base sm:text-lg",children:e.description})]})]},t))})]})}),(0,a.jsx)(Y,{}),(0,a.jsx)("section",{id:"faq",className:"py-12 sm:py-20 bg-slate-800/30",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-3 sm:px-6 lg:px-8",children:[(0,a.jsxs)(c.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-12 sm:mb-16",children:[(0,a.jsx)("h2",{className:"text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-3 sm:mb-4",children:"Frequently Asked Questions"}),(0,a.jsx)("p",{className:"text-lg sm:text-xl text-gray-300 max-w-2xl mx-auto px-2",children:"Everything you need to know about downloading YouTube subtitles"})]}),(0,a.jsx)(k.A,{showHeader:!1})]})}),(0,a.jsx)("section",{className:"py-12 sm:py-20 bg-slate-800/50",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-3 sm:px-6 lg:px-8",children:[(0,a.jsxs)(c.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-12",children:[(0,a.jsx)("h2",{className:"text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-4 sm:mb-6",children:"Choose Your Plan"}),(0,a.jsx)("p",{className:"text-lg sm:text-xl text-gray-300 max-w-3xl mx-auto",children:"Professional subtitle extraction with flexible pricing for every need"})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 sm:gap-8 mb-12",children:Object.entries(A.D).map((e,t)=>{let[s,i]=e;return(0,a.jsx)(c.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.1*t},viewport:{once:!0},className:"relative",children:(0,a.jsxs)(f.Zp,{className:"relative overflow-hidden ".concat(i.popular?"border-purple-500 shadow-lg shadow-purple-500/20 scale-105":"border-slate-700"," bg-slate-800/80 backdrop-blur-sm h-full"),children:[i.popular&&(0,a.jsx)("div",{className:"absolute top-0 left-0 right-0",children:(0,a.jsx)("div",{className:"bg-gradient-to-r from-purple-500 to-pink-500 text-white text-center py-2 text-sm font-medium",children:"Most Popular"})}),(0,a.jsxs)(f.aR,{className:i.popular?"pt-12":"pt-6",children:[(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)(f.ZB,{className:"text-white flex items-center gap-2",children:["starter"===s&&(0,a.jsx)(u.A,{className:"w-5 h-5 text-blue-500"}),"pro"===s&&(0,a.jsx)(b.A,{className:"w-5 h-5 text-purple-500"}),"premium"===s&&(0,a.jsx)(g.A,{className:"w-5 h-5 text-yellow-500"}),i.name]})}),(0,a.jsx)(f.BT,{className:"text-gray-300",children:(0,a.jsxs)("div",{className:"flex items-baseline gap-1",children:[(0,a.jsxs)("span",{className:"text-3xl font-bold text-white",children:["$",i.price]}),(0,a.jsx)("span",{className:"text-gray-400",children:"/month"})]})})]}),(0,a.jsx)(f.Wu,{className:"space-y-4",children:(0,a.jsx)("ul",{className:"space-y-2",children:i.features.slice(0,4).map((e,t)=>(0,a.jsxs)("li",{className:"flex items-start gap-2 text-sm",children:[(0,a.jsx)("div",{className:"w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-gray-300",children:e})]},t))})})]})},s)})}),(0,a.jsx)(c.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.4},viewport:{once:!0},className:"text-center",children:(0,a.jsxs)(y.$,{onClick:s,variant:"outline",size:"lg",className:"border-purple-500 text-purple-400 hover:bg-purple-500 hover:text-white",children:["View All Plans & Features",(0,a.jsx)(w.A,{className:"w-4 h-4 ml-2"})]})})]})}),(0,a.jsx)("section",{className:"py-12 sm:py-20 bg-gradient-to-r from-purple-800/20 to-pink-800/20",children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto px-3 sm:px-6 lg:px-8 text-center",children:(0,a.jsxs)(c.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},children:[(0,a.jsx)("h2",{className:"text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-4 sm:mb-6",children:"Ready to Extract Subtitles?"}),(0,a.jsx)("p",{className:"text-lg sm:text-xl text-gray-300 mb-6 sm:mb-8 px-2",children:"Start extracting professional-quality subtitles in seconds"}),(0,a.jsxs)(y.$,{onClick:t,size:"lg",className:"w-full sm:w-auto bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 sm:px-12 py-3 sm:py-4 text-lg sm:text-xl font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300",children:[(0,a.jsx)(x.A,{className:"w-5 h-5 sm:w-6 sm:h-6 mr-2"}),"Start Extracting Now"]})]})})})]})};function S(){let e=(0,o.useRouter)(),t=t=>{e.push("/".concat(t))};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(l(),{children:[(0,a.jsx)("title",{children:"Download YouTube Subtitles - Free YT Caption Extractor - DownloadYTSubtitles"}),(0,a.jsx)("meta",{name:"description",content:"Free YouTube subtitle downloader and transcript extractor. Download YT captions in VTT and TXT formats from auto-generated and manual subtitles."}),(0,a.jsx)("meta",{name:"keywords",content:"YouTube subtitles, download YouTube captions, YouTube transcript extractor, YT subtitles, video captions"}),(0,a.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes"}),(0,a.jsx)("link",{rel:"icon",href:"/favicon.ico"})]}),(0,a.jsxs)("div",{className:"min-h-screen font-sans",children:[(0,a.jsx)(n.A,{currentView:"landing",onNavigate:t,onFeedback:()=>{window.open("https://docs.google.com/forms/d/e/1FAIpQLSd1zn2jbwo7UKAkoRSPIV2RIxt2ZNi4VQYLqC8S0bV7CRZe_Q/viewform?usp=dialog","_blank")}}),(0,a.jsx)(P,{onGetStarted:()=>{e.push("/extractor")},onNavigateToPricing:()=>t("pricing")}),(0,a.jsx)(r.A,{onTermsClick:()=>{e.push("/terms")},onPrivacyClick:()=>{e.push("/privacy")},onDisclaimerClick:()=>{e.push("/disclaimer")}})]})]})}},6093:(e,t,s)=>{"use strict";s.d(t,{A:()=>g});var a=s(7876),i=s(4232),l=s(1886),o=s(7212),n=s(8638),r=s(6960),c=s(9812),d=s(2467),m=s(7297),u=s(5390),x=s(2212),p=s(769),h=s(1575),b=s(822);let g=e=>{let{showHeader:t=!0}=e,[s,g]=(0,i.useState)([0]),w=e=>{g(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},y=[{question:"Can I download subtitles from any YouTube video?",answer:"Yes, you can download subtitles from any YouTube video that has subtitles available. This includes both manually uploaded subtitles and auto-generated captions. Our YouTube subtitle downloader works with videos in all languages and formats.",icon:(0,a.jsx)(c.A,{className:"w-5 h-5"}),keywords:"download YouTube subtitles, YouTube video subtitles, extract YouTube captions"},{question:"Are auto-generated YouTube subtitles supported?",answer:"Absolutely! Our YouTube transcript extractor can fetch auto-generated captions from any video. Auto-generated subtitles are created by YouTube's AI and are available for most videos. You can download these in SRT, VTT, or TXT format.",icon:(0,a.jsx)(d.A,{className:"w-5 h-5"}),keywords:"auto-generated YouTube captions, YouTube AI subtitles, automatic captions download"},{question:"Is it legal to download subtitles from YouTube videos?",answer:"For personal, educational, or non-commercial use, downloading YouTube subtitles is generally acceptable under fair use. Always credit the original creator and respect copyright laws. Commercial use may require permission from the content owner.",icon:(0,a.jsx)(m.A,{className:"w-5 h-5"}),keywords:"legal YouTube subtitle download, fair use YouTube captions, copyright YouTube subtitles"},{question:"What subtitle formats can I download?",answer:"Our YouTube subtitle downloader supports two popular formats: VTT (WebVTT) and TXT (plain text with metadata). VTT works great for web videos and video players, while TXT includes video metadata and timestamped content perfect for reading transcripts.",icon:(0,a.jsx)(u.A,{className:"w-5 h-5"}),keywords:"VTT YouTube subtitles, YouTube transcript TXT format, WebVTT download"},{question:"How accurate are YouTube auto-generated subtitles?",answer:"YouTube's auto-generated subtitles have improved significantly and are generally 80-95% accurate for clear speech in popular languages like English. Accuracy may vary based on audio quality, accents, and technical terminology. Always review before important use.",icon:(0,a.jsx)(x.A,{className:"w-5 h-5"}),keywords:"YouTube auto-caption accuracy, YouTube AI subtitle quality, auto-generated captions reliability"},{question:"Can I download subtitles in different languages?",answer:"Yes! If a YouTube video has subtitles in multiple languages, you can choose and download any available language. Our tool supports 70+ languages including English, Spanish, French, German, Japanese, Korean, Arabic, Hindi, and many more.",icon:(0,a.jsx)(p.A,{className:"w-5 h-5"}),keywords:"multilingual YouTube subtitles, YouTube captions different languages, international YouTube transcripts"},{question:"Do I need to create an account to download subtitles?",answer:"No account required! Our YouTube subtitle extractor is completely free and doesn't require registration. Simply paste the YouTube URL, select your preferred language and format, then download instantly. We respect your privacy and don't store any personal data.",icon:(0,a.jsx)(m.A,{className:"w-5 h-5"}),keywords:"free YouTube subtitle download, no registration YouTube captions, anonymous subtitle extractor"},{question:"Can I download subtitles from YouTube playlists?",answer:"Currently, our tool focuses on individual YouTube videos for the best user experience. You can extract subtitles from each video in a playlist by processing them one at a time. This ensures higher quality and more reliable downloads.",icon:(0,a.jsx)(c.A,{className:"w-5 h-5"}),keywords:"YouTube video subtitles, individual video captions, single video transcript download"},{question:"How fast is the subtitle extraction process?",answer:"Our YouTube subtitle downloader typically extracts and processes subtitles within 5-15 seconds, depending on the video length and subtitle complexity. The process includes fetching, cleaning, and formatting the subtitles for optimal readability.",icon:(0,a.jsx)(d.A,{className:"w-5 h-5"}),keywords:"fast YouTube subtitle download, quick caption extraction, instant YouTube transcript"},{question:"What should I do if subtitles aren't available for a video?",answer:"If a YouTube video doesn't have subtitles, our tool will notify you. The video creator may not have uploaded subtitles, or auto-generation might be disabled. Try checking if the video has captions enabled in YouTube's settings first.",icon:(0,a.jsx)(x.A,{className:"w-5 h-5"}),keywords:"YouTube video no subtitles, missing YouTube captions, subtitle availability check"}];return(0,a.jsx)("div",{className:t?"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900":"",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[t&&(0,a.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"text-center mb-12",children:[(0,a.jsx)("h1",{className:"text-4xl md:text-5xl font-bold text-white mb-4",children:"Frequently Asked Questions"}),(0,a.jsx)("p",{className:"text-xl text-gray-300 max-w-2xl mx-auto",children:"Everything you need to know about downloading YouTube subtitles and captions"})]}),(0,a.jsx)("div",{className:"space-y-4",children:y.map((e,i)=>(0,a.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:t?.1*(i+1):.05*i},children:(0,a.jsxs)(n.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700 overflow-hidden",children:[(0,a.jsx)(r.$,{variant:"ghost",onClick:()=>w(i),className:"w-full p-6 text-left hover:bg-slate-700/50 transition-colors",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"text-purple-400",children:e.icon}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:e.question})]}),(0,a.jsx)("div",{className:"text-purple-400",children:s.includes(i)?(0,a.jsx)(h.A,{className:"w-5 h-5"}):(0,a.jsx)(b.A,{className:"w-5 h-5"})})]})}),(0,a.jsx)(o.N,{children:s.includes(i)&&(0,a.jsx)(l.P.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},transition:{duration:.3},children:(0,a.jsx)(n.Wu,{className:"px-6 pb-6 pt-0",children:(0,a.jsx)("div",{className:"pl-9",children:(0,a.jsx)("p",{className:"text-gray-300 leading-relaxed",children:e.answer})})})})})]})},i))}),t&&(0,a.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},className:"mt-12 text-center",children:(0,a.jsx)(n.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700",children:(0,a.jsxs)(n.Wu,{className:"p-8",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-4",children:"Still have questions?"}),(0,a.jsx)("p",{className:"text-gray-300 mb-6",children:"Can't find the answer you're looking for? We'd love to help you get the most out of our YouTube subtitle downloader."}),(0,a.jsx)(r.$,{onClick:()=>{window.open("https://docs.google.com/forms/d/e/1FAIpQLSd1zn2jbwo7UKAkoRSPIV2RIxt2ZNi4VQYLqC8S0bV7CRZe_Q/viewform?usp=dialog","_blank")},className:"bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700",children:"Contact Support"})]})})})]})})}},6760:(e,t,s)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/",function(){return s(3817)}])}},e=>{var t=t=>e(e.s=t);e.O(0,[587,893,817,882,701,636,593,792],()=>t(6760)),_N_E=e.O()}]);