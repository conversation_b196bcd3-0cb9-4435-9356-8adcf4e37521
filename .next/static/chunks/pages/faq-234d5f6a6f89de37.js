(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[816],{769:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(1713).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},822:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(1713).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},1575:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(1713).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},2467:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(1713).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},2924:(e,t,s)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/faq",function(){return s(9637)}])},6093:(e,t,s)=>{"use strict";s.d(t,{A:()=>x});var a=s(7876),o=s(4232),i=s(1886),n=s(7212),l=s(8638),r=s(6960),u=s(9812),d=s(2467),c=s(7297),p=s(5390),h=s(2212),m=s(769),b=s(1575),f=s(822);let x=e=>{let{showHeader:t=!0}=e,[s,x]=(0,o.useState)([0]),w=e=>{x(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},y=[{question:"Can I download subtitles from any YouTube video?",answer:"Yes, you can download subtitles from any YouTube video that has subtitles available. This includes both manually uploaded subtitles and auto-generated captions. Our YouTube subtitle downloader works with videos in all languages and formats.",icon:(0,a.jsx)(u.A,{className:"w-5 h-5"}),keywords:"download YouTube subtitles, YouTube video subtitles, extract YouTube captions"},{question:"Are auto-generated YouTube subtitles supported?",answer:"Absolutely! Our YouTube transcript extractor can fetch auto-generated captions from any video. Auto-generated subtitles are created by YouTube's AI and are available for most videos. You can download these in SRT, VTT, or TXT format.",icon:(0,a.jsx)(d.A,{className:"w-5 h-5"}),keywords:"auto-generated YouTube captions, YouTube AI subtitles, automatic captions download"},{question:"Is it legal to download subtitles from YouTube videos?",answer:"For personal, educational, or non-commercial use, downloading YouTube subtitles is generally acceptable under fair use. Always credit the original creator and respect copyright laws. Commercial use may require permission from the content owner.",icon:(0,a.jsx)(c.A,{className:"w-5 h-5"}),keywords:"legal YouTube subtitle download, fair use YouTube captions, copyright YouTube subtitles"},{question:"What subtitle formats can I download?",answer:"Our YouTube subtitle downloader supports two popular formats: VTT (WebVTT) and TXT (plain text with metadata). VTT works great for web videos and video players, while TXT includes video metadata and timestamped content perfect for reading transcripts.",icon:(0,a.jsx)(p.A,{className:"w-5 h-5"}),keywords:"VTT YouTube subtitles, YouTube transcript TXT format, WebVTT download"},{question:"How accurate are YouTube auto-generated subtitles?",answer:"YouTube's auto-generated subtitles have improved significantly and are generally 80-95% accurate for clear speech in popular languages like English. Accuracy may vary based on audio quality, accents, and technical terminology. Always review before important use.",icon:(0,a.jsx)(h.A,{className:"w-5 h-5"}),keywords:"YouTube auto-caption accuracy, YouTube AI subtitle quality, auto-generated captions reliability"},{question:"Can I download subtitles in different languages?",answer:"Yes! If a YouTube video has subtitles in multiple languages, you can choose and download any available language. Our tool supports 70+ languages including English, Spanish, French, German, Japanese, Korean, Arabic, Hindi, and many more.",icon:(0,a.jsx)(m.A,{className:"w-5 h-5"}),keywords:"multilingual YouTube subtitles, YouTube captions different languages, international YouTube transcripts"},{question:"Do I need to create an account to download subtitles?",answer:"No account required! Our YouTube subtitle extractor is completely free and doesn't require registration. Simply paste the YouTube URL, select your preferred language and format, then download instantly. We respect your privacy and don't store any personal data.",icon:(0,a.jsx)(c.A,{className:"w-5 h-5"}),keywords:"free YouTube subtitle download, no registration YouTube captions, anonymous subtitle extractor"},{question:"Can I download subtitles from YouTube playlists?",answer:"Currently, our tool focuses on individual YouTube videos for the best user experience. You can extract subtitles from each video in a playlist by processing them one at a time. This ensures higher quality and more reliable downloads.",icon:(0,a.jsx)(u.A,{className:"w-5 h-5"}),keywords:"YouTube video subtitles, individual video captions, single video transcript download"},{question:"How fast is the subtitle extraction process?",answer:"Our YouTube subtitle downloader typically extracts and processes subtitles within 5-15 seconds, depending on the video length and subtitle complexity. The process includes fetching, cleaning, and formatting the subtitles for optimal readability.",icon:(0,a.jsx)(d.A,{className:"w-5 h-5"}),keywords:"fast YouTube subtitle download, quick caption extraction, instant YouTube transcript"},{question:"What should I do if subtitles aren't available for a video?",answer:"If a YouTube video doesn't have subtitles, our tool will notify you. The video creator may not have uploaded subtitles, or auto-generation might be disabled. Try checking if the video has captions enabled in YouTube's settings first.",icon:(0,a.jsx)(h.A,{className:"w-5 h-5"}),keywords:"YouTube video no subtitles, missing YouTube captions, subtitle availability check"}];return(0,a.jsx)("div",{className:t?"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900":"",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[t&&(0,a.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"text-center mb-12",children:[(0,a.jsx)("h1",{className:"text-4xl md:text-5xl font-bold text-white mb-4",children:"Frequently Asked Questions"}),(0,a.jsx)("p",{className:"text-xl text-gray-300 max-w-2xl mx-auto",children:"Everything you need to know about downloading YouTube subtitles and captions"})]}),(0,a.jsx)("div",{className:"space-y-4",children:y.map((e,o)=>(0,a.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:t?.1*(o+1):.05*o},children:(0,a.jsxs)(l.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700 overflow-hidden",children:[(0,a.jsx)(r.$,{variant:"ghost",onClick:()=>w(o),className:"w-full p-6 text-left hover:bg-slate-700/50 transition-colors",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"text-purple-400",children:e.icon}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:e.question})]}),(0,a.jsx)("div",{className:"text-purple-400",children:s.includes(o)?(0,a.jsx)(b.A,{className:"w-5 h-5"}):(0,a.jsx)(f.A,{className:"w-5 h-5"})})]})}),(0,a.jsx)(n.N,{children:s.includes(o)&&(0,a.jsx)(i.P.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},transition:{duration:.3},children:(0,a.jsx)(l.Wu,{className:"px-6 pb-6 pt-0",children:(0,a.jsx)("div",{className:"pl-9",children:(0,a.jsx)("p",{className:"text-gray-300 leading-relaxed",children:e.answer})})})})})]})},o))}),t&&(0,a.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},className:"mt-12 text-center",children:(0,a.jsx)(l.Zp,{className:"bg-slate-800/80 backdrop-blur-sm border-slate-700",children:(0,a.jsxs)(l.Wu,{className:"p-8",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-4",children:"Still have questions?"}),(0,a.jsx)("p",{className:"text-gray-300 mb-6",children:"Can't find the answer you're looking for? We'd love to help you get the most out of our YouTube subtitle downloader."}),(0,a.jsx)(r.$,{onClick:()=>{window.open("https://docs.google.com/forms/d/e/1FAIpQLSd1zn2jbwo7UKAkoRSPIV2RIxt2ZNi4VQYLqC8S0bV7CRZe_Q/viewform?usp=dialog","_blank")},className:"bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700",children:"Contact Support"})]})})})]})})}},7212:(e,t,s)=>{"use strict";s.d(t,{N:()=>x});var a=s(7876),o=s(4232),i=s(5048),n=s(1200),l=s(3866),r=s(9751);class u extends o.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=this.props.sizeRef.current;e.height=t.offsetHeight||0,e.width=t.offsetWidth||0,e.top=t.offsetTop,e.left=t.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function d({children:e,isPresent:t}){let s=(0,o.useId)(),i=(0,o.useRef)(null),n=(0,o.useRef)({width:0,height:0,top:0,left:0}),{nonce:l}=(0,o.useContext)(r.Q);return(0,o.useInsertionEffect)(()=>{let{width:e,height:a,top:o,left:r}=n.current;if(t||!i.current||!e||!a)return;i.current.dataset.motionPopId=s;let u=document.createElement("style");return l&&(u.nonce=l),document.head.appendChild(u),u.sheet&&u.sheet.insertRule(`
          [data-motion-pop-id="${s}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${a}px !important;
            top: ${o}px !important;
            left: ${r}px !important;
          }
        `),()=>{document.head.removeChild(u)}},[t]),(0,a.jsx)(u,{isPresent:t,childRef:i,sizeRef:n,children:o.cloneElement(e,{ref:i})})}let c=({children:e,initial:t,isPresent:s,onExitComplete:i,custom:r,presenceAffectsLayout:u,mode:c})=>{let h=(0,n.M)(p),m=(0,o.useId)(),b=(0,o.useCallback)(e=>{for(let t of(h.set(e,!0),h.values()))if(!t)return;i&&i()},[h,i]),f=(0,o.useMemo)(()=>({id:m,initial:t,isPresent:s,custom:r,onExitComplete:b,register:e=>(h.set(e,!1),()=>h.delete(e))}),u?[Math.random(),b]:[s,b]);return(0,o.useMemo)(()=>{h.forEach((e,t)=>h.set(t,!1))},[s]),o.useEffect(()=>{s||h.size||!i||i()},[s]),"popLayout"===c&&(e=(0,a.jsx)(d,{isPresent:s,children:e})),(0,a.jsx)(l.t.Provider,{value:f,children:e})};function p(){return new Map}var h=s(3885);let m=e=>e.key||"";function b(e){let t=[];return o.Children.forEach(e,e=>{(0,o.isValidElement)(e)&&t.push(e)}),t}var f=s(181);let x=({children:e,custom:t,initial:s=!0,onExitComplete:l,presenceAffectsLayout:r=!0,mode:u="sync",propagate:d=!1})=>{let[p,x]=(0,h.xQ)(d),w=(0,o.useMemo)(()=>b(e),[e]),y=d&&!p?[]:w.map(m),g=(0,o.useRef)(!0),v=(0,o.useRef)(w),T=(0,n.M)(()=>new Map),[j,Y]=(0,o.useState)(w),[k,N]=(0,o.useState)(w);(0,f.E)(()=>{g.current=!1,v.current=w;for(let e=0;e<k.length;e++){let t=m(k[e]);y.includes(t)?T.delete(t):!0!==T.get(t)&&T.set(t,!1)}},[k,y.length,y.join("-")]);let A=[];if(w!==j){let e=[...w];for(let t=0;t<k.length;t++){let s=k[t],a=m(s);y.includes(a)||(e.splice(t,0,s),A.push(s))}"wait"===u&&A.length&&(e=A),N(b(e)),Y(w);return}let{forceRender:q}=(0,o.useContext)(i.L);return(0,a.jsx)(a.Fragment,{children:k.map(e=>{let o=m(e),i=(!d||!!p)&&(w===k||y.includes(o));return(0,a.jsx)(c,{isPresent:i,initial:(!g.current||!!s)&&void 0,custom:i?void 0:t,presenceAffectsLayout:r,mode:u,onExitComplete:i?void 0:()=>{if(!T.has(o))return;T.set(o,!0);let e=!0;T.forEach(t=>{t||(e=!1)}),e&&(null==q||q(),N(v.current),d&&(null==x||x()),l&&l())},children:e},o)})})}},9637:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var a=s(7876),o=s(7328),i=s.n(o),n=s(9099),l=s(8606),r=s(4518),u=s(6093);function d(){let e=(0,n.useRouter)();return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(i(),{children:[(0,a.jsx)("title",{children:"FAQ - DownloadYTSubtitles"}),(0,a.jsx)("meta",{name:"description",content:"Frequently Asked Questions about DownloadYTSubtitles - YouTube subtitle extractor service."}),(0,a.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes"}),(0,a.jsx)("link",{rel:"icon",href:"/favicon.ico"})]}),(0,a.jsxs)("div",{className:"min-h-screen font-sans",children:[(0,a.jsx)(l.A,{currentView:"faq",onNavigate:t=>{e.push("/".concat(t))},onFeedback:()=>{window.open("https://docs.google.com/forms/d/e/1FAIpQLSd1zn2jbwo7UKAkoRSPIV2RIxt2ZNi4VQYLqC8S0bV7CRZe_Q/viewform?usp=dialog","_blank")}}),(0,a.jsx)(u.A,{showHeader:!0}),(0,a.jsx)(r.A,{onTermsClick:()=>{e.push("/terms")},onPrivacyClick:()=>{e.push("/privacy")},onDisclaimerClick:()=>{e.push("/disclaimer")}})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[587,893,817,701,636,593,792],()=>t(2924)),_N_E=e.O()}]);