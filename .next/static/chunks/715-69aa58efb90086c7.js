"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[715],{491:(e,t,n)=>{n.d(t,{B8:()=>C,UC:()=>j,bL:()=>W,l9:()=>N});var a=n(4232),r=n(3716),i=n(1844),o=n(4455),u=n(6822),l=n(6326),s=n(4966),d=n(8162),c=n(294),h=n(7876),m="Tabs",[f,g]=(0,i.A)(m,[o.RG]),w=(0,o.RG)(),[b,v]=f(m),y=a.forwardRef((e,t)=>{let{__scopeTabs:n,value:a,onValueChange:r,defaultValue:i,orientation:o="horizontal",dir:u,activationMode:f="automatic",...g}=e,w=(0,s.jH)(u),[v,y]=(0,d.i)({prop:a,onChange:r,defaultProp:i??"",caller:m});return(0,h.jsx)(b,{scope:n,baseId:(0,c.B)(),value:v,onValueChange:y,orientation:o,dir:w,activationMode:f,children:(0,h.jsx)(l.sG.div,{dir:w,"data-orientation":o,...g,ref:t})})});y.displayName=m;var p="TabsList",M=a.forwardRef((e,t)=>{let{__scopeTabs:n,loop:a=!0,...r}=e,i=v(p,n),u=w(n);return(0,h.jsx)(o.bL,{asChild:!0,...u,orientation:i.orientation,dir:i.dir,loop:a,children:(0,h.jsx)(l.sG.div,{role:"tablist","aria-orientation":i.orientation,...r,ref:t})})});M.displayName=p;var x="TabsTrigger",k=a.forwardRef((e,t)=>{let{__scopeTabs:n,value:a,disabled:i=!1,...u}=e,s=v(x,n),d=w(n),c=S(s.baseId,a),m=T(s.baseId,a),f=a===s.value;return(0,h.jsx)(o.q7,{asChild:!0,...d,focusable:!i,active:f,children:(0,h.jsx)(l.sG.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":m,"data-state":f?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:c,...u,ref:t,onMouseDown:(0,r.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():s.onValueChange(a)}),onKeyDown:(0,r.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&s.onValueChange(a)}),onFocus:(0,r.m)(e.onFocus,()=>{let e="manual"!==s.activationMode;f||i||!e||s.onValueChange(a)})})})});k.displayName=x;var P="TabsContent",D=a.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,forceMount:i,children:o,...s}=e,d=v(P,n),c=S(d.baseId,r),m=T(d.baseId,r),f=r===d.value,g=a.useRef(f);return a.useEffect(()=>{let e=requestAnimationFrame(()=>g.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,h.jsx)(u.C,{present:i||f,children:({present:n})=>(0,h.jsx)(l.sG.div,{"data-state":f?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":c,hidden:!n,id:m,tabIndex:0,...s,ref:t,style:{...e.style,animationDuration:g.current?"0s":void 0},children:n&&o})})});function S(e,t){return`${e}-trigger-${t}`}function T(e,t){return`${e}-content-${t}`}D.displayName=P;var W=y,C=M,N=k,j=D},1899:(e,t,n)=>{n.d(t,{A:()=>a});let a=(0,n(1713).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},2885:(e,t,n)=>{n.d(t,{A:()=>a});let a=(0,n(1713).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},3654:(e,t,n)=>{n.d(t,{A:()=>a});let a=(0,n(1713).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},4972:(e,t,n)=>{n.d(t,{C1:()=>M,bL:()=>p});var a=n(4232),r=n(1844),i=n(6326),o=n(7876),u="Progress",[l,s]=(0,r.A)(u),[d,c]=l(u),h=a.forwardRef((e,t)=>{var n,a;let{__scopeProgress:r,value:u=null,max:l,getValueLabel:s=g,...c}=e;(l||0===l)&&!v(l)&&console.error((n=`${l}`,`Invalid prop \`max\` of value \`${n}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let h=v(l)?l:100;null===u||y(u,h)||console.error((a=`${u}`,`Invalid prop \`value\` of value \`${a}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let m=y(u,h)?u:null,f=b(m)?s(m,h):void 0;return(0,o.jsx)(d,{scope:r,value:m,max:h,children:(0,o.jsx)(i.sG.div,{"aria-valuemax":h,"aria-valuemin":0,"aria-valuenow":b(m)?m:void 0,"aria-valuetext":f,role:"progressbar","data-state":w(m,h),"data-value":m??void 0,"data-max":h,...c,ref:t})})});h.displayName=u;var m="ProgressIndicator",f=a.forwardRef((e,t)=>{let{__scopeProgress:n,...a}=e,r=c(m,n);return(0,o.jsx)(i.sG.div,{"data-state":w(r.value,r.max),"data-value":r.value??void 0,"data-max":r.max,...a,ref:t})});function g(e,t){return`${Math.round(e/t*100)}%`}function w(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function b(e){return"number"==typeof e}function v(e){return b(e)&&!isNaN(e)&&e>0}function y(e,t){return b(e)&&!isNaN(e)&&e<=t&&e>=0}f.displayName=m;var p=h,M=f},5851:(e,t,n)=>{n.d(t,{A:()=>a});let a=(0,n(1713).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},8572:(e,t,n)=>{n.d(t,{A:()=>a});let a=(0,n(1713).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},9468:(e,t,n)=>{n.d(t,{GP:()=>E});let a={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function r(e){return function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.width?String(t.width):e.defaultWidth;return e.formats[n]||e.formats[e.defaultWidth]}}let i={date:r({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:r({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:r({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},o={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function u(e){return(t,n)=>{let a;if("formatting"===((null==n?void 0:n.context)?String(n.context):"standalone")&&e.formattingValues){let t=e.defaultFormattingWidth||e.defaultWidth,r=(null==n?void 0:n.width)?String(n.width):t;a=e.formattingValues[r]||e.formattingValues[t]}else{let t=e.defaultWidth,r=(null==n?void 0:n.width)?String(n.width):e.defaultWidth;a=e.values[r]||e.values[t]}return a[e.argumentCallback?e.argumentCallback(t):t]}}function l(e){return function(t){let n,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=a.width,i=r&&e.matchPatterns[r]||e.matchPatterns[e.defaultMatchWidth],o=t.match(i);if(!o)return null;let u=o[0],l=r&&e.parsePatterns[r]||e.parsePatterns[e.defaultParseWidth],s=Array.isArray(l)?function(e,t){for(let n=0;n<e.length;n++)if(t(e[n]))return n}(l,e=>e.test(u)):function(e,t){for(let n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&t(e[n]))return n}(l,e=>e.test(u));return n=e.valueCallback?e.valueCallback(s):s,{value:n=a.valueCallback?a.valueCallback(n):n,rest:t.slice(u.length)}}}let s={code:"en-US",formatDistance:(e,t,n)=>{let r,i=a[e];if(r="string"==typeof i?i:1===t?i.one:i.other.replace("{{count}}",t.toString()),null==n?void 0:n.addSuffix)if(n.comparison&&n.comparison>0)return"in "+r;else return r+" ago";return r},formatLong:i,formatRelative:(e,t,n,a)=>o[e],localize:{ordinalNumber:(e,t)=>{let n=Number(e),a=n%100;if(a>20||a<10)switch(a%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},era:u({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:u({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:u({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:u({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:u({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:function(e){return function(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=t.match(e.matchPattern);if(!a)return null;let r=a[0],i=t.match(e.parsePattern);if(!i)return null;let o=e.valueCallback?e.valueCallback(i[0]):i[0];return{value:o=n.valueCallback?n.valueCallback(o):o,rest:t.slice(r.length)}}}({matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:l({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:l({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:l({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:l({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:l({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}},d={};function c(e){let t=Object.prototype.toString.call(e);return e instanceof Date||"object"==typeof e&&"[object Date]"===t?new e.constructor(+e):new Date("number"==typeof e||"[object Number]"===t||"string"==typeof e||"[object String]"===t?e:NaN)}function h(e){let t=c(e);return t.setHours(0,0,0,0),t}function m(e){let t=c(e),n=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return n.setUTCFullYear(t.getFullYear()),e-n}function f(e,t){return e instanceof Date?new e.constructor(t):new Date(t)}function g(e,t){var n,a,r,i,o,u,l,s;let h=null!=(s=null!=(l=null!=(u=null!=(o=null==t?void 0:t.weekStartsOn)?o:null==t||null==(a=t.locale)||null==(n=a.options)?void 0:n.weekStartsOn)?u:d.weekStartsOn)?l:null==(i=d.locale)||null==(r=i.options)?void 0:r.weekStartsOn)?s:0,m=c(e),f=m.getDay();return m.setDate(m.getDate()-(7*(f<h)+f-h)),m.setHours(0,0,0,0),m}function w(e){return g(e,{weekStartsOn:1})}function b(e){let t=c(e),n=t.getFullYear(),a=f(e,0);a.setFullYear(n+1,0,4),a.setHours(0,0,0,0);let r=w(a),i=f(e,0);i.setFullYear(n,0,4),i.setHours(0,0,0,0);let o=w(i);return t.getTime()>=r.getTime()?n+1:t.getTime()>=o.getTime()?n:n-1}function v(e,t){var n,a,r,i,o,u,l,s;let h=c(e),m=h.getFullYear(),w=null!=(s=null!=(l=null!=(u=null!=(o=null==t?void 0:t.firstWeekContainsDate)?o:null==t||null==(a=t.locale)||null==(n=a.options)?void 0:n.firstWeekContainsDate)?u:d.firstWeekContainsDate)?l:null==(i=d.locale)||null==(r=i.options)?void 0:r.firstWeekContainsDate)?s:1,b=f(e,0);b.setFullYear(m+1,0,w),b.setHours(0,0,0,0);let v=g(b,t),y=f(e,0);y.setFullYear(m,0,w),y.setHours(0,0,0,0);let p=g(y,t);return h.getTime()>=v.getTime()?m+1:h.getTime()>=p.getTime()?m:m-1}function y(e,t){let n=Math.abs(e).toString().padStart(t,"0");return(e<0?"-":"")+n}let p={y(e,t){let n=e.getFullYear(),a=n>0?n:1-n;return y("yy"===t?a%100:a,t.length)},M(e,t){let n=e.getMonth();return"M"===t?String(n+1):y(n+1,2)},d:(e,t)=>y(e.getDate(),t.length),a(e,t){let n=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];default:return"am"===n?"a.m.":"p.m."}},h:(e,t)=>y(e.getHours()%12||12,t.length),H:(e,t)=>y(e.getHours(),t.length),m:(e,t)=>y(e.getMinutes(),t.length),s:(e,t)=>y(e.getSeconds(),t.length),S(e,t){let n=t.length;return y(Math.trunc(e.getMilliseconds()*Math.pow(10,n-3)),t.length)}},M={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},x={G:function(e,t,n){let a=+(e.getFullYear()>0);switch(t){case"G":case"GG":case"GGG":return n.era(a,{width:"abbreviated"});case"GGGGG":return n.era(a,{width:"narrow"});default:return n.era(a,{width:"wide"})}},y:function(e,t,n){if("yo"===t){let t=e.getFullYear();return n.ordinalNumber(t>0?t:1-t,{unit:"year"})}return p.y(e,t)},Y:function(e,t,n,a){let r=v(e,a),i=r>0?r:1-r;return"YY"===t?y(i%100,2):"Yo"===t?n.ordinalNumber(i,{unit:"year"}):y(i,t.length)},R:function(e,t){return y(b(e),t.length)},u:function(e,t){return y(e.getFullYear(),t.length)},Q:function(e,t,n){let a=Math.ceil((e.getMonth()+1)/3);switch(t){case"Q":return String(a);case"QQ":return y(a,2);case"Qo":return n.ordinalNumber(a,{unit:"quarter"});case"QQQ":return n.quarter(a,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(a,{width:"narrow",context:"formatting"});default:return n.quarter(a,{width:"wide",context:"formatting"})}},q:function(e,t,n){let a=Math.ceil((e.getMonth()+1)/3);switch(t){case"q":return String(a);case"qq":return y(a,2);case"qo":return n.ordinalNumber(a,{unit:"quarter"});case"qqq":return n.quarter(a,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(a,{width:"narrow",context:"standalone"});default:return n.quarter(a,{width:"wide",context:"standalone"})}},M:function(e,t,n){let a=e.getMonth();switch(t){case"M":case"MM":return p.M(e,t);case"Mo":return n.ordinalNumber(a+1,{unit:"month"});case"MMM":return n.month(a,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(a,{width:"narrow",context:"formatting"});default:return n.month(a,{width:"wide",context:"formatting"})}},L:function(e,t,n){let a=e.getMonth();switch(t){case"L":return String(a+1);case"LL":return y(a+1,2);case"Lo":return n.ordinalNumber(a+1,{unit:"month"});case"LLL":return n.month(a,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(a,{width:"narrow",context:"standalone"});default:return n.month(a,{width:"wide",context:"standalone"})}},w:function(e,t,n,a){let r=function(e,t){let n=c(e);return Math.round((g(n,t)-function(e,t){var n,a,r,i,o,u,l,s;let c=null!=(s=null!=(l=null!=(u=null!=(o=null==t?void 0:t.firstWeekContainsDate)?o:null==t||null==(a=t.locale)||null==(n=a.options)?void 0:n.firstWeekContainsDate)?u:d.firstWeekContainsDate)?l:null==(i=d.locale)||null==(r=i.options)?void 0:r.firstWeekContainsDate)?s:1,h=v(e,t),m=f(e,0);return m.setFullYear(h,0,c),m.setHours(0,0,0,0),g(m,t)}(n,t))/6048e5)+1}(e,a);return"wo"===t?n.ordinalNumber(r,{unit:"week"}):y(r,t.length)},I:function(e,t,n){let a=function(e){let t=c(e);return Math.round((w(t)-function(e){let t=b(e),n=f(e,0);return n.setFullYear(t,0,4),n.setHours(0,0,0,0),w(n)}(t))/6048e5)+1}(e);return"Io"===t?n.ordinalNumber(a,{unit:"week"}):y(a,t.length)},d:function(e,t,n){return"do"===t?n.ordinalNumber(e.getDate(),{unit:"date"}):p.d(e,t)},D:function(e,t,n){let a=function(e){let t=c(e);return function(e,t){let n=h(e),a=h(t);return Math.round((n-m(n)-(a-m(a)))/864e5)}(t,function(e){let t=c(e),n=f(e,0);return n.setFullYear(t.getFullYear(),0,1),n.setHours(0,0,0,0),n}(t))+1}(e);return"Do"===t?n.ordinalNumber(a,{unit:"dayOfYear"}):y(a,t.length)},E:function(e,t,n){let a=e.getDay();switch(t){case"E":case"EE":case"EEE":return n.day(a,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(a,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(a,{width:"short",context:"formatting"});default:return n.day(a,{width:"wide",context:"formatting"})}},e:function(e,t,n,a){let r=e.getDay(),i=(r-a.weekStartsOn+8)%7||7;switch(t){case"e":return String(i);case"ee":return y(i,2);case"eo":return n.ordinalNumber(i,{unit:"day"});case"eee":return n.day(r,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(r,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},c:function(e,t,n,a){let r=e.getDay(),i=(r-a.weekStartsOn+8)%7||7;switch(t){case"c":return String(i);case"cc":return y(i,t.length);case"co":return n.ordinalNumber(i,{unit:"day"});case"ccc":return n.day(r,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(r,{width:"narrow",context:"standalone"});case"cccccc":return n.day(r,{width:"short",context:"standalone"});default:return n.day(r,{width:"wide",context:"standalone"})}},i:function(e,t,n){let a=e.getDay(),r=0===a?7:a;switch(t){case"i":return String(r);case"ii":return y(r,t.length);case"io":return n.ordinalNumber(r,{unit:"day"});case"iii":return n.day(a,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(a,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(a,{width:"short",context:"formatting"});default:return n.day(a,{width:"wide",context:"formatting"})}},a:function(e,t,n){let a=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(a,{width:"narrow",context:"formatting"});default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},b:function(e,t,n){let a,r=e.getHours();switch(a=12===r?M.noon:0===r?M.midnight:r/12>=1?"pm":"am",t){case"b":case"bb":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(a,{width:"narrow",context:"formatting"});default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},B:function(e,t,n){let a,r=e.getHours();switch(a=r>=17?M.evening:r>=12?M.afternoon:r>=4?M.morning:M.night,t){case"B":case"BB":case"BBB":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(a,{width:"narrow",context:"formatting"});default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},h:function(e,t,n){if("ho"===t){let t=e.getHours()%12;return 0===t&&(t=12),n.ordinalNumber(t,{unit:"hour"})}return p.h(e,t)},H:function(e,t,n){return"Ho"===t?n.ordinalNumber(e.getHours(),{unit:"hour"}):p.H(e,t)},K:function(e,t,n){let a=e.getHours()%12;return"Ko"===t?n.ordinalNumber(a,{unit:"hour"}):y(a,t.length)},k:function(e,t,n){let a=e.getHours();return(0===a&&(a=24),"ko"===t)?n.ordinalNumber(a,{unit:"hour"}):y(a,t.length)},m:function(e,t,n){return"mo"===t?n.ordinalNumber(e.getMinutes(),{unit:"minute"}):p.m(e,t)},s:function(e,t,n){return"so"===t?n.ordinalNumber(e.getSeconds(),{unit:"second"}):p.s(e,t)},S:function(e,t){return p.S(e,t)},X:function(e,t,n){let a=e.getTimezoneOffset();if(0===a)return"Z";switch(t){case"X":return P(a);case"XXXX":case"XX":return D(a);default:return D(a,":")}},x:function(e,t,n){let a=e.getTimezoneOffset();switch(t){case"x":return P(a);case"xxxx":case"xx":return D(a);default:return D(a,":")}},O:function(e,t,n){let a=e.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+k(a,":");default:return"GMT"+D(a,":")}},z:function(e,t,n){let a=e.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+k(a,":");default:return"GMT"+D(a,":")}},t:function(e,t,n){return y(Math.trunc(e.getTime()/1e3),t.length)},T:function(e,t,n){return y(e.getTime(),t.length)}};function k(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=e>0?"-":"+",a=Math.abs(e),r=Math.trunc(a/60),i=a%60;return 0===i?n+String(r):n+String(r)+t+y(i,2)}function P(e,t){return e%60==0?(e>0?"-":"+")+y(Math.abs(e)/60,2):D(e,t)}function D(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=Math.abs(e);return(e>0?"-":"+")+y(Math.trunc(n/60),2)+t+y(n%60,2)}let S=(e,t)=>{switch(e){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});default:return t.date({width:"full"})}},T=(e,t)=>{switch(e){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});default:return t.time({width:"full"})}},W={p:T,P:(e,t)=>{let n,a=e.match(/(P+)(p+)?/)||[],r=a[1],i=a[2];if(!i)return S(e,t);switch(r){case"P":n=t.dateTime({width:"short"});break;case"PP":n=t.dateTime({width:"medium"});break;case"PPP":n=t.dateTime({width:"long"});break;default:n=t.dateTime({width:"full"})}return n.replace("{{date}}",S(r,t)).replace("{{time}}",T(i,t))}},C=/^D+$/,N=/^Y+$/,j=["D","DD","YY","YYYY"],Y=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,q=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,O=/^'([^]*?)'?$/,A=/''/g,F=/[a-zA-Z]/;function E(e,t,n){var a,r,i,o,u,l,h,m,f,g,w,b,v,y,p,M,k,P;let D=null!=(g=null!=(f=null==n?void 0:n.locale)?f:d.locale)?g:s,S=null!=(y=null!=(v=null!=(b=null!=(w=null==n?void 0:n.firstWeekContainsDate)?w:null==n||null==(r=n.locale)||null==(a=r.options)?void 0:a.firstWeekContainsDate)?b:d.firstWeekContainsDate)?v:null==(o=d.locale)||null==(i=o.options)?void 0:i.firstWeekContainsDate)?y:1,T=null!=(P=null!=(k=null!=(M=null!=(p=null==n?void 0:n.weekStartsOn)?p:null==n||null==(l=n.locale)||null==(u=l.options)?void 0:u.weekStartsOn)?M:d.weekStartsOn)?k:null==(m=d.locale)||null==(h=m.options)?void 0:h.weekStartsOn)?P:0,E=c(e);if(!((E instanceof Date||"object"==typeof E&&"[object Date]"===Object.prototype.toString.call(E)||"number"==typeof E)&&!isNaN(Number(c(E)))))throw RangeError("Invalid time value");let H=t.match(q).map(e=>{let t=e[0];return"p"===t||"P"===t?(0,W[t])(e,D.formatLong):e}).join("").match(Y).map(e=>{if("''"===e)return{isToken:!1,value:"'"};let t=e[0];if("'"===t)return{isToken:!1,value:function(e){let t=e.match(O);return t?t[1].replace(A,"'"):e}(e)};if(x[t])return{isToken:!0,value:e};if(t.match(F))throw RangeError("Format string contains an unescaped latin alphabet character `"+t+"`");return{isToken:!1,value:e}});D.localize.preprocessor&&(H=D.localize.preprocessor(E,H));let G={firstWeekContainsDate:S,weekStartsOn:T,locale:D};return H.map(a=>{if(!a.isToken)return a.value;let r=a.value;return(!(null==n?void 0:n.useAdditionalWeekYearTokens)&&N.test(r)||!(null==n?void 0:n.useAdditionalDayOfYearTokens)&&C.test(r))&&function(e,t,n){let a=function(e,t,n){let a="Y"===e[0]?"years":"days of the month";return"Use `".concat(e.toLowerCase(),"` instead of `").concat(e,"` (in `").concat(t,"`) for formatting ").concat(a," to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md")}(e,t,n);if(console.warn(a),j.includes(e))throw RangeError(a)}(r,t,String(e)),(0,x[r[0]])(E,r,D.localize,G)}).join("")}}}]);