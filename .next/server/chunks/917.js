exports.id=917,exports.ids=[917],exports.modules={417:(e,t,r)=>{"use strict";r.d(t,{N:()=>o});var a=r(3939);let s="https://vsaxiialrhpqdlcpmetn.supabase.co",i="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.MDe8-SLF1-73hqcunFsgOEvDYek1ku7Ap_A1UNardA8";s&&i&&s.includes("placeholder");let o=(0,a.createClient)(s,i,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"pkce"}})},798:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{A:()=>d,O:()=>u});var s=r(8732),i=r(2015),o=r(417),n=r(2893),l=e([n]);n=(l.then?(await l)():l)[0];let c=(0,i.createContext)(void 0),d=()=>{let e=(0,i.useContext)(c);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},u=({children:e})=>{let[t,r]=(0,i.useState)(null),[a,l]=(0,i.useState)(!0),[d,u]=(0,i.useState)(null);(0,i.useEffect)(()=>{(async()=>{try{let{data:{session:e},error:t}=await o.N.auth.getSession();t?(console.error("Error getting session:",t),u(t.message)):r(e?.user||null)}catch(e){console.error("Error in getInitialSession:",e),u("Failed to get session")}finally{l(!1)}})();let{data:{subscription:e}}=o.N.auth.onAuthStateChange(async(e,t)=>{console.log("Auth state changed:",e,t?.user?.email),"SIGNED_IN"===e||"TOKEN_REFRESHED"===e?(r(t?.user||null),u(null),t?.user&&await p(t.user)):"SIGNED_OUT"===e&&(r(null),u(null)),l(!1)});return()=>e.unsubscribe()},[]);let p=async e=>{try{let{error:t}=await o.N.from("users").upsert({id:e.id,email:e.email||"",full_name:e.user_metadata?.full_name||e.user_metadata?.name||null,avatar_url:e.user_metadata?.avatar_url||e.user_metadata?.picture||null,updated_at:new Date().toISOString()},{onConflict:"id"});t&&console.error("Error creating/updating user profile:",t)}catch(e){console.error("Error in createOrUpdateUserProfile:",e)}},h=async()=>{try{l(!0),u(null);let{error:e}=await o.N.auth.signInWithOAuth({provider:"google",options:{redirectTo:`${window.location.origin}/auth/callback`}});e&&(u(e.message),n.default.error("Failed to sign in with Google"))}catch(e){console.error("Error signing in with Google:",e),u("Failed to sign in"),n.default.error("Failed to sign in with Google")}finally{l(!1)}},f=async()=>{try{l(!0),u(null);let{error:e}=await o.N.auth.signOut();e?(u(e.message),n.default.error("Failed to sign out")):n.default.success("Signed out successfully")}catch(e){console.error("Error signing out:",e),u("Failed to sign out"),n.default.error("Failed to sign out")}finally{l(!1)}},g=async()=>{try{let{data:{user:e},error:t}=await o.N.auth.getUser();t?u(t.message):r(e)}catch(e){console.error("Error refreshing user:",e),u("Failed to refresh user")}};return(0,s.jsx)(c.Provider,{value:{user:t,loading:a,error:d,signInWithGoogle:h,signOut:f,refreshUser:g},children:e})};a()}catch(e){a(e)}})},849:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{R:()=>d});var s=r(2015),i=r(417),o=r(2796),n=r(798),l=r(2893),c=e([n,l]);[n,l]=c.then?(await c)():c;let d=()=>{let{user:e}=(0,n.A)(),[t,r]=(0,s.useState)(null),[a,c]=(0,s.useState)(null),[d,u]=(0,s.useState)(!0),[p,h]=(0,s.useState)(null);(0,s.useEffect)(()=>{e?f():(r(null),c(null),u(!1))},[e]);let f=async()=>{if(e)try{u(!0),h(null);let{data:{session:e}}=await i.N.auth.getSession(),t=e?.access_token?{Authorization:`Bearer ${e.access_token}`}:{},a=await fetch("/api/user/credits",{headers:{"Content-Type":"application/json",...t}});if(!a.ok)throw Error("Failed to fetch credit data");let s=await a.json();r(s.userCredits),c(s.usage)}catch(e){console.error("Error fetching credit data:",e),h("Failed to fetch credit data")}finally{u(!1)}},g=(e=1)=>!!t&&t.available_credits>=e,m=()=>t?t.available_credits:0,y=async t=>{if(!e)throw Error("User must be authenticated");try{let r=await fetch("/api/stripe/checkout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({priceId:o.D[t].priceId,userId:e.id,userEmail:e.email})});if(!r.ok)throw Error("Failed to create checkout session");let{sessionId:a}=await r.json();return a}catch(e){throw console.error("Error creating checkout session:",e),l.default.error("Failed to create checkout session"),e}},w=async()=>{throw l.default.error("Credit packs cannot be cancelled. Credits are valid for 6 months."),Error("Credit packs cannot be cancelled")},v=async()=>{await f()};return{subscription:t?{id:t.id,user_id:t.user_id,stripe_subscription_id:"",stripe_customer_id:"",status:t.available_credits>0?"active":"inactive",tier:"pro",current_period_start:t.created_at,current_period_end:t.last_purchase_date,cancel_at_period_end:!1,created_at:t.created_at,updated_at:t.updated_at}:null,usage:a,loading:d,error:p,canPerformAction:g,canExtractVideo:()=>g(1),getRemainingCredits:m,getRemainingExtractions:()=>m(),getUsageInfo:()=>t?{creditsPerAction:1,actionsPerExtraction:1}:null,shouldWarnAboutCredits:(e=1)=>{let t=m();return t<=10||t<e},getUsageSuggestions:()=>{let e=m(),t=[];return e<=5&&t.push({type:"warning",message:"Low credits remaining. Consider purchasing more credits.",action:"upgrade"}),e<=2&&t.push({type:"error",message:"Very low credits. Use English default to save credits.",action:"use-english-default"}),0===e&&t.push({type:"error",message:"No credits remaining. Purchase a credit pack to continue.",action:"buy-credits"}),t},createCheckoutSession:y,cancelSubscription:w,refreshSubscription:v,userCredits:t}};a()}catch(e){a(e)}})},1518:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{BK:()=>d,eu:()=>c,q5:()=>u});var s=r(8732),i=r(2015),o=r(2549),n=r(3678),l=e([o,n]);[o,n]=l.then?(await l)():l;let c=i.forwardRef(({className:e,...t},r)=>(0,s.jsx)(o.Root,{ref:r,className:(0,n.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...t}));c.displayName=o.Root.displayName;let d=i.forwardRef(({className:e,...t},r)=>(0,s.jsx)(o.Image,{ref:r,className:(0,n.cn)("aspect-square h-full w-full",e),...t}));d.displayName=o.Image.displayName;let u=i.forwardRef(({className:e,...t},r)=>(0,s.jsx)(o.Fallback,{ref:r,className:(0,n.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...t}));u.displayName=o.Fallback.displayName,a()}catch(e){a(e)}})},2237:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{BT:()=>u,Wu:()=>p,ZB:()=>d,Zp:()=>l,aR:()=>c,wL:()=>h});var s=r(8732),i=r(2015),o=r(3678),n=e([o]);o=(n.then?(await n)():n)[0];let l=i.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,o.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...t}));l.displayName="Card";let c=i.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",e),...t}));c.displayName="CardHeader";let d=i.forwardRef(({className:e,...t},r)=>(0,s.jsx)("h3",{ref:r,className:(0,o.cn)("font-semibold leading-none tracking-tight",e),...t}));d.displayName="CardTitle";let u=i.forwardRef(({className:e,...t},r)=>(0,s.jsx)("p",{ref:r,className:(0,o.cn)("text-sm text-muted-foreground",e),...t}));u.displayName="CardDescription";let p=i.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,o.cn)("p-6 pt-0",e),...t}));p.displayName="CardContent";let h=i.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,o.cn)("flex items-center p-6 pt-0",e),...t}));h.displayName="CardFooter",a()}catch(e){a(e)}})},2386:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{default:()=>l});var s=r(8732),i=r(2893),o=r(798);r(2768);var n=e([i,o]);function l({Component:e,pageProps:t}){return(0,s.jsxs)(o.O,{children:[(0,s.jsx)(e,{...t}),(0,s.jsx)(i.Toaster,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#1e293b",color:"#f1f5f9",border:"1px solid #475569"}}})]})}[i,o]=n.then?(await n)():n,a()}catch(e){a(e)}})},2768:()=>{},2796:(e,t,r)=>{"use strict";r.d(t,{D:()=>o,t:()=>i});var a=r(206);let s="pk_test_51Rbz5DG3dq2cZKhWQAhsMvCKvlJ7qki27vhezUpnbMECmp53uG6V3JmEy7pQodTCBWYNKeP55X64ZIJxaXfkijrM00xhD0btGs",i=s?(0,a.loadStripe)(s):null,o={starter:{name:"Starter",price:10,priceId:"price_1Rbz9CG3dq2cZKhWCnIVHUx6",features:["50 credits (~50 subtitle downloads)","VTT and TXT format downloads","Manual and Auto-generated caption support","English subtitle extraction","Multi-language support","Credits valid for 6 months"],limits:{creditsPerMonth:50,creditsPerAction:1,actionsPerExtraction:1},popular:!1},pro:{name:"Pro",price:30,priceId:"price_1RbzCCG3dq2cZKhWS3MlXiCZ",features:["200 credits (~200 subtitle downloads)","VTT and TXT format downloads","Manual and Auto-generated caption support","English subtitle extraction","Multi-language support","Credits valid for 6 months"],limits:{creditsPerMonth:200,creditsPerAction:1,actionsPerExtraction:1},popular:!0},creator:{name:"Creator",price:75,priceId:"price_1RbzDHG3dq2cZKhWID6C0aMY",features:["600 credits (~600 subtitle downloads)","VTT and TXT format downloads","Manual and Auto-generated caption support","English subtitle extraction","Multi-language support","Credits valid for 6 months"],limits:{creditsPerMonth:600,creditsPerAction:1,actionsPerExtraction:1},popular:!1}}},3678:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{cn:()=>n});var s=r(802),i=r(5979),o=e([s,i]);function n(...e){return(0,i.twMerge)((0,s.clsx)(e))}[s,i]=o.then?(await o)():o,a()}catch(e){a(e)}})},7459:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{$:()=>u});var s=r(8732),i=r(2015),o=r(9640),n=r(8938),l=r(3678),c=e([o,n,l]);[o,n,l]=c.then?(await c)():c;let d=(0,n.cva)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),u=i.forwardRef(({className:e,variant:t,size:r,asChild:a=!1,...i},n)=>{let c=a?o.Slot:"button";return(0,s.jsx)(c,{className:(0,l.cn)(d({variant:t,size:r,className:e})),ref:n,...i})});u.displayName="Button",a()}catch(e){a(e)}})}};