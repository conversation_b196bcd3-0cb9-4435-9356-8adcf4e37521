"use strict";exports.id=323,exports.ids=[323],exports.modules={3996:(e,s,t)=>{t.a(e,async(e,a)=>{try{t.d(s,{A:()=>d});var l=t(8732);t(2015);var i=t(7459),r=t(798),n=t(1335),c=t(4961),o=e([i,r]);[i,r]=o.then?(await o)():o;let d=({variant:e="default",size:s="default",className:t="",children:a})=>{let{signInWithGoogle:o,loading:d}=(0,r.A)();return(0,l.jsxs)(i.$,{onClick:o,disabled:d,variant:e,size:s,className:t,children:[d?(0,l.jsx)(n.A,{className:"w-4 h-4 mr-2 animate-spin"}):(0,l.jsx)(c.A,{className:"w-4 h-4 mr-2"}),a||"Sign in with Google"]})};a()}catch(e){a(e)}})},4275:(e,s,t)=>{t.a(e,async(e,a)=>{try{t.d(s,{A:()=>f});var l=t(8732);t(2015);var i=t(7459),r=t(4474),n=t(1518),c=t(798),o=t(849),d=t(2312),m=t(3838),x=t(8903),h=t(9048),p=t(1576),u=e([i,r,n,c,o]);[i,r,n,c,o]=u.then?(await u)():u;let f=({onNavigate:e})=>{let{user:s,signOut:t}=(0,c.A)(),{subscription:a}=(0,o.R)();if(!s)return null;let u=s.user_metadata?.full_name||s.user_metadata?.name||s.email?.split("@")[0]||"User",f=s.user_metadata?.avatar_url||s.user_metadata?.picture;return(0,l.jsxs)(r.rI,{children:[(0,l.jsx)(r.ty,{asChild:!0,children:(0,l.jsx)(i.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,l.jsxs)(n.eu,{className:"h-8 w-8",children:[(0,l.jsx)(n.BK,{src:f,alt:u}),(0,l.jsx)(n.q5,{className:"bg-purple-600 text-white",children:u.split(" ").map(e=>e[0]).join("").toUpperCase().slice(0,2)})]})})}),(0,l.jsxs)(r.SQ,{className:"w-56",align:"end",forceMount:!0,children:[(0,l.jsx)(r.lp,{className:"font-normal",children:(0,l.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,l.jsx)("p",{className:"text-sm font-medium leading-none",children:u}),(0,l.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:s.email}),a&&(0,l.jsxs)("div",{className:"flex items-center gap-1 mt-1",children:[(0,l.jsx)(d.A,{className:"w-3 h-3 text-yellow-500"}),(0,l.jsxs)("span",{className:"text-xs font-medium text-yellow-600 capitalize",children:[a.tier," Plan"]})]})]})}),(0,l.jsx)(r.mB,{}),(0,l.jsxs)(r._2,{onClick:()=>e("dashboard"),children:[(0,l.jsx)(m.A,{className:"mr-2 h-4 w-4"}),(0,l.jsx)("span",{children:"Dashboard"})]}),(0,l.jsxs)(r._2,{onClick:()=>e("pricing"),children:[(0,l.jsx)(x.A,{className:"mr-2 h-4 w-4"}),(0,l.jsx)("span",{children:"Subscription"})]}),(0,l.jsxs)(r._2,{onClick:()=>e("settings"),children:[(0,l.jsx)(h.A,{className:"mr-2 h-4 w-4"}),(0,l.jsx)("span",{children:"Settings"})]}),(0,l.jsx)(r.mB,{}),(0,l.jsxs)(r._2,{onClick:t,children:[(0,l.jsx)(p.A,{className:"mr-2 h-4 w-4"}),(0,l.jsx)("span",{children:"Sign out"})]})]})]})};a()}catch(e){a(e)}})},4474:(e,s,t)=>{t.a(e,async(e,a)=>{try{t.d(s,{SQ:()=>x,_2:()=>h,lp:()=>p,mB:()=>u,rI:()=>d,ty:()=>m});var l=t(8732),i=t(2015),r=t(6307),n=t(824),c=t(3678),o=e([r,c]);[r,c]=o.then?(await o)():o;let d=r.Root,m=r.Trigger;r.Group,r.Portal,r.Sub,r.RadioGroup,i.forwardRef(({className:e,inset:s,children:t,...a},i)=>(0,l.jsxs)(r.SubTrigger,{ref:i,className:(0,c.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",s&&"pl-8",e),...a,children:[t,(0,l.jsx)(n.ChevronRightIcon,{className:"ml-auto h-4 w-4"})]})).displayName=r.SubTrigger.displayName,i.forwardRef(({className:e,...s},t)=>(0,l.jsx)(r.SubContent,{ref:t,className:(0,c.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...s})).displayName=r.SubContent.displayName;let x=i.forwardRef(({className:e,sideOffset:s=4,...t},a)=>(0,l.jsx)(r.Portal,{children:(0,l.jsx)(r.Content,{ref:a,sideOffset:s,className:(0,c.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t})}));x.displayName=r.Content.displayName;let h=i.forwardRef(({className:e,inset:s,...t},a)=>(0,l.jsx)(r.Item,{ref:a,className:(0,c.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s&&"pl-8",e),...t}));h.displayName=r.Item.displayName,i.forwardRef(({className:e,children:s,checked:t,...a},i)=>(0,l.jsxs)(r.CheckboxItem,{ref:i,className:(0,c.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:t,...a,children:[(0,l.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,l.jsx)(r.ItemIndicator,{children:(0,l.jsx)(n.CheckIcon,{className:"h-4 w-4"})})}),s]})).displayName=r.CheckboxItem.displayName,i.forwardRef(({className:e,children:s,...t},a)=>(0,l.jsxs)(r.RadioItem,{ref:a,className:(0,c.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[(0,l.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,l.jsx)(r.ItemIndicator,{children:(0,l.jsx)(n.DotFilledIcon,{className:"h-4 w-4 fill-current"})})}),s]})).displayName=r.RadioItem.displayName;let p=i.forwardRef(({className:e,inset:s,...t},a)=>(0,l.jsx)(r.Label,{ref:a,className:(0,c.cn)("px-2 py-1.5 text-sm font-semibold",s&&"pl-8",e),...t}));p.displayName=r.Label.displayName;let u=i.forwardRef(({className:e,...s},t)=>(0,l.jsx)(r.Separator,{ref:t,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",e),...s}));u.displayName=r.Separator.displayName,a()}catch(e){a(e)}})},5027:(e,s,t)=>{t.a(e,async(e,a)=>{try{t.d(s,{A:()=>x});var l=t(8732),i=t(824),r=t(3220),n=t(2554),c=t(8509),o=t(6596),d=t(703),m=e([r]);r=(m.then?(await m)():m)[0];let x=({onTermsClick:e,onPrivacyClick:s,onDisclaimerClick:t})=>{let a=new Date().getFullYear(),m={product:[{name:"Features",href:"#features"},{name:"How to Use",href:"#how-to-use"},{name:"Use Cases",href:"#use-cases"},{name:"FAQ",href:"#faq"}],legal:[{name:"Privacy Policy",onClick:s},{name:"Terms of Service",onClick:e},{name:"Disclaimer",onClick:t}],social:[{name:"GitHub",href:"#",icon:(0,l.jsx)(i.GitHubLogoIcon,{className:"w-5 h-5"})},{name:"Twitter",href:"#",icon:(0,l.jsx)(i.TwitterLogoIcon,{className:"w-5 h-5"})},{name:"Email",href:"mailto:<EMAIL>",icon:(0,l.jsx)(n.A,{className:"w-5 h-5"})}]};return(0,l.jsx)("footer",{className:"bg-slate-900 border-t border-slate-800",children:(0,l.jsxs)("div",{className:"max-w-7xl mx-auto px-3 sm:px-6 lg:px-8 py-8 sm:py-12",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8",children:[(0,l.jsxs)(r.motion.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"col-span-1 sm:col-span-2",children:[(0,l.jsxs)("h3",{className:"text-xl sm:text-2xl font-bold text-white mb-3 sm:mb-4",children:["YouTube Subtitle",(0,l.jsxs)("span",{className:"bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent",children:[" ","Extractor"]})]}),(0,l.jsx)("p",{className:"text-gray-400 mb-4 sm:mb-6 max-w-md text-sm sm:text-base",children:"The most advanced YouTube subtitle extraction tool. Extract, clean, and download subtitles from videos and playlists with professional-grade accuracy."}),(0,l.jsx)("div",{className:"flex space-x-3 sm:space-x-4",children:m.social.map(e=>(0,l.jsx)(r.motion.a,{href:e.href,whileHover:{scale:1.1},whileTap:{scale:.95},className:"text-gray-400 hover:text-purple-400 transition-colors duration-200","aria-label":e.name,children:e.icon},e.name))})]}),(0,l.jsxs)(r.motion.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1},viewport:{once:!0},children:[(0,l.jsx)("h4",{className:"text-base sm:text-lg font-semibold text-white mb-3 sm:mb-4",children:"Product"}),(0,l.jsx)("ul",{className:"space-y-1 sm:space-y-2",children:m.product.map(e=>(0,l.jsx)("li",{children:(0,l.jsx)("a",{href:e.href,className:"text-gray-400 hover:text-purple-400 transition-colors duration-200 text-sm sm:text-base",children:e.name})},e.name))})]}),(0,l.jsxs)(r.motion.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},viewport:{once:!0},children:[(0,l.jsx)("h4",{className:"text-base sm:text-lg font-semibold text-white mb-3 sm:mb-4",children:"Legal"}),(0,l.jsx)("ul",{className:"space-y-1 sm:space-y-2",children:m.legal.map(e=>(0,l.jsx)("li",{children:e.onClick?(0,l.jsx)("button",{onClick:e.onClick,className:"text-gray-400 hover:text-purple-400 transition-colors duration-200 text-sm sm:text-base",children:e.name}):(0,l.jsx)("a",{className:"text-gray-400 hover:text-purple-400 transition-colors duration-200 text-sm sm:text-base",children:e.name})},e.name))})]})]}),(0,l.jsxs)(r.motion.div,{initial:{opacity:0},whileInView:{opacity:1},transition:{duration:.6,delay:.4},viewport:{once:!0},className:"flex flex-col lg:flex-row justify-between items-center pt-6 sm:pt-8 mt-6 sm:mt-8 border-t border-slate-800 space-y-4 lg:space-y-0",children:[(0,l.jsxs)("div",{className:"flex items-center text-gray-400 text-xs sm:text-sm text-center lg:text-left",children:[(0,l.jsxs)("span",{children:["\xa9 ",a," DownloadYTSubtitles.com. Made with"]}),(0,l.jsx)(c.A,{className:"w-3 h-3 sm:w-4 sm:h-4 text-red-400 mx-1"}),(0,l.jsx)("span",{children:"for the community."})]}),(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-4 lg:space-x-6 text-xs sm:text-sm text-gray-400",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,l.jsx)(o.A,{className:"w-3 h-3 sm:w-4 sm:h-4"}),(0,l.jsx)("span",{children:"Privacy First"})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,l.jsx)(d.A,{className:"w-3 h-3 sm:w-4 sm:h-4"}),(0,l.jsx)("span",{children:"Professional Quality"})]})]})]})]})})};a()}catch(e){a(e)}})},8405:(e,s,t)=>{t.a(e,async(e,a)=>{try{t.d(s,{A:()=>f});var l=t(8732),i=t(3220),r=t(7459),n=t(3295),c=t(6569),o=t(3441),d=t(8903),m=t(128),x=t(798),h=t(3996),p=t(4275),u=e([i,r,x,h,p]);[i,r,x,h,p]=u.then?(await u)():u;let f=({currentView:e,onNavigate:s,onFeedback:t})=>{let{user:a}=(0,x.A)();return(0,l.jsx)(i.motion.header,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"bg-slate-900/95 backdrop-blur-sm border-b border-slate-700 sticky top-0 z-50",children:(0,l.jsxs)("div",{className:"max-w-7xl mx-auto px-3 sm:px-6 lg:px-8",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between h-14 sm:h-16",children:[(0,l.jsxs)(i.motion.div,{whileHover:{scale:1.05},className:"flex items-center space-x-2 sm:space-x-3 cursor-pointer",onClick:()=>s(""),children:[(0,l.jsx)("div",{className:"w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg flex items-center justify-center",children:(0,l.jsx)(n.A,{className:"w-4 h-4 sm:w-6 sm:h-6 text-white"})}),(0,l.jsxs)("div",{className:"hidden xs:block",children:[(0,l.jsx)("h1",{className:"text-lg sm:text-xl font-bold text-white",children:"DownloadYTSubtitles"}),(0,l.jsx)("p",{className:"text-xs text-gray-400 hidden sm:block",children:"YouTube Subtitle Extractor"})]}),(0,l.jsx)("div",{className:"block xs:hidden",children:(0,l.jsx)("h1",{className:"text-sm font-bold text-white",children:"DYTS"})})]}),(0,l.jsxs)("nav",{className:"hidden md:flex items-center space-x-6",children:[(0,l.jsxs)(r.$,{variant:"landing"===e?"default":"ghost",size:"sm",onClick:()=>s(""),className:"landing"===e?"bg-purple-600 hover:bg-purple-700":"text-gray-300 hover:text-white",children:[(0,l.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"Home"]}),(0,l.jsxs)(r.$,{variant:"extractor"===e?"default":"ghost",size:"sm",onClick:()=>s("extractor"),className:"extractor"===e?"bg-purple-600 hover:bg-purple-700":"text-gray-300 hover:text-white",children:[(0,l.jsx)(n.A,{className:"w-4 h-4 mr-2"}),"Extract"]}),(0,l.jsxs)(r.$,{variant:"faq"===e?"default":"ghost",size:"sm",onClick:()=>s("faq"),className:"faq"===e?"bg-purple-600 hover:bg-purple-700":"text-gray-300 hover:text-white",children:[(0,l.jsx)(o.A,{className:"w-4 h-4 mr-2"}),"FAQ"]}),(0,l.jsxs)(r.$,{variant:"pricing"===e?"default":"ghost",size:"sm",onClick:()=>s("pricing"),className:"pricing"===e?"bg-purple-600 hover:bg-purple-700":"text-gray-300 hover:text-white",children:[(0,l.jsx)(d.A,{className:"w-4 h-4 mr-2"}),"Pricing"]})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-2 sm:space-x-3",children:[(0,l.jsxs)(r.$,{variant:"outline",size:"sm",onClick:t,className:"border-purple-500 text-purple-400 hover:bg-purple-500 hover:text-white text-xs sm:text-sm px-2 sm:px-3",children:[(0,l.jsx)(m.A,{className:"w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2"}),(0,l.jsx)("span",{className:"hidden xs:inline",children:"Feedback"}),(0,l.jsx)("span",{className:"xs:hidden",children:"FB"})]}),a?(0,l.jsx)(p.A,{onNavigate:s}):(0,l.jsx)(h.A,{variant:"outline",size:"sm",className:"border-purple-600 text-purple-400 hover:bg-purple-600 hover:text-white"}),(0,l.jsx)("div",{className:"md:hidden",children:(0,l.jsx)(r.$,{variant:"ghost",size:"sm",onClick:()=>s("extractor"),className:"text-gray-300 hover:text-white p-2",children:(0,l.jsx)(n.A,{className:"w-4 h-4 sm:w-5 sm:h-5"})})})]})]}),(0,l.jsx)("div",{className:"md:hidden pb-3",children:(0,l.jsxs)("div",{className:"flex items-center justify-center space-x-2 overflow-x-auto",children:[(0,l.jsx)(r.$,{variant:"landing"===e?"default":"ghost",size:"sm",onClick:()=>s(""),className:`text-xs px-3 py-2 ${"landing"===e?"bg-purple-600 hover:bg-purple-700":"text-gray-300 hover:text-white"}`,children:"Home"}),(0,l.jsx)(r.$,{variant:"extractor"===e?"default":"ghost",size:"sm",onClick:()=>s("extractor"),className:`text-xs px-3 py-2 ${"extractor"===e?"bg-purple-600 hover:bg-purple-700":"text-gray-300 hover:text-white"}`,children:"Extract"}),(0,l.jsx)(r.$,{variant:"faq"===e?"default":"ghost",size:"sm",onClick:()=>s("faq"),className:`text-xs px-3 py-2 ${"faq"===e?"bg-purple-600 hover:bg-purple-700":"text-gray-300 hover:text-white"}`,children:"FAQ"}),(0,l.jsx)(r.$,{variant:"pricing"===e?"default":"ghost",size:"sm",onClick:()=>s("pricing"),className:`text-xs px-3 py-2 ${"pricing"===e?"bg-purple-600 hover:bg-purple-700":"text-gray-300 hover:text-white"}`,children:"Pricing"})]})})]})})};a()}catch(e){a(e)}})}};