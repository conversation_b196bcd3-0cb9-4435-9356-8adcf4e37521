(()=>{var e={};e.id=636,e.ids=[636],e.modules={417:(e,t,r)=>{"use strict";r.d(t,{N:()=>o});var a=r(3939);let s="https://vsaxiialrhpqdlcpmetn.supabase.co",i="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.MDe8-SLF1-73hqcunFsgOEvDYek1ku7Ap_A1UNardA8";s&&i&&s.includes("placeholder");let o=(0,a.createClient)(s,i,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"pkce"}})},798:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{A:()=>c,O:()=>d});var s=r(8732),i=r(2015),o=r(417),n=r(2893),l=e([n]);n=(l.then?(await l)():l)[0];let u=(0,i.createContext)(void 0),c=()=>{let e=(0,i.useContext)(u);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},d=({children:e})=>{let[t,r]=(0,i.useState)(null),[a,l]=(0,i.useState)(!0),[c,d]=(0,i.useState)(null);(0,i.useEffect)(()=>{(async()=>{try{let{data:{session:e},error:t}=await o.N.auth.getSession();t?(console.error("Error getting session:",t),d(t.message)):r(e?.user||null)}catch(e){console.error("Error in getInitialSession:",e),d("Failed to get session")}finally{l(!1)}})();let{data:{subscription:e}}=o.N.auth.onAuthStateChange(async(e,t)=>{console.log("Auth state changed:",e,t?.user?.email),"SIGNED_IN"===e||"TOKEN_REFRESHED"===e?(r(t?.user||null),d(null),t?.user&&await h(t.user)):"SIGNED_OUT"===e&&(r(null),d(null)),l(!1)});return()=>e.unsubscribe()},[]);let h=async e=>{try{let{error:t}=await o.N.from("users").upsert({id:e.id,email:e.email||"",full_name:e.user_metadata?.full_name||e.user_metadata?.name||null,avatar_url:e.user_metadata?.avatar_url||e.user_metadata?.picture||null,updated_at:new Date().toISOString()},{onConflict:"id"});t&&console.error("Error creating/updating user profile:",t)}catch(e){console.error("Error in createOrUpdateUserProfile:",e)}},g=async()=>{try{l(!0),d(null);let{error:e}=await o.N.auth.signInWithOAuth({provider:"google",options:{redirectTo:`${window.location.origin}/auth/callback`}});e&&(d(e.message),n.default.error("Failed to sign in with Google"))}catch(e){console.error("Error signing in with Google:",e),d("Failed to sign in"),n.default.error("Failed to sign in with Google")}finally{l(!1)}},p=async()=>{try{l(!0),d(null);let{error:e}=await o.N.auth.signOut();e?(d(e.message),n.default.error("Failed to sign out")):n.default.success("Signed out successfully")}catch(e){console.error("Error signing out:",e),d("Failed to sign out"),n.default.error("Failed to sign out")}finally{l(!1)}},f=async()=>{try{let{data:{user:e},error:t}=await o.N.auth.getUser();t?d(t.message):r(e)}catch(e){console.error("Error refreshing user:",e),d("Failed to refresh user")}};return(0,s.jsx)(u.Provider,{value:{user:t,loading:a,error:c,signInWithGoogle:g,signOut:p,refreshUser:f},children:e})};a()}catch(e){a(e)}})},2015:e=>{"use strict";e.exports=require("react")},2386:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{default:()=>l});var s=r(8732),i=r(2893),o=r(798);r(2768);var n=e([i,o]);function l({Component:e,pageProps:t}){return(0,s.jsxs)(o.O,{children:[(0,s.jsx)(e,{...t}),(0,s.jsx)(i.Toaster,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#1e293b",color:"#f1f5f9",border:"1px solid #475569"}}})]})}[i,o]=n.then?(await n)():n,a()}catch(e){a(e)}})},2768:()=>{},2893:e=>{"use strict";e.exports=import("react-hot-toast")},3939:e=>{"use strict";e.exports=require("@supabase/supabase-js")},8732:e=>{"use strict";e.exports=require("react/jsx-runtime")}};var t=require("../webpack-runtime.js");t.C(e);var r=t(t.s=2386);module.exports=r})();