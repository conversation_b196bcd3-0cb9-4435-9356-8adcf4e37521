"use strict";(()=>{var e={};e.id=550,e.ids=[550],e.modules={262:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{config:()=>u,default:()=>c,routeModule:()=>l});var o=r(3480),a=r(8667),i=r(6435),n=r(4100),d=e([n]);n=(d.then?(await d)():d)[0];let c=(0,i.M)(n,"default"),u=(0,i.M)(n,"config"),l=new o.PagesAPIRouteModule({definition:{kind:a.A.PAGES_API,page:"/api/stripe/webhook",pathname:"/api/stripe/webhook",bundlePath:"",filename:""},userland:n});s()}catch(e){s(e)}})},3480:(e,t,r)=>{e.exports=r(5600)},3939:e=>{e.exports=require("@supabase/supabase-js")},4100:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{config:()=>u,default:()=>n});var o=r(9767),a=r(6157),i=e([o]);o=(i.then?(await i)():i)[0];let u={api:{bodyParser:!1}},l=new o.default(process.env.STRIPE_SECRET_KEY),p=process.env.STRIPE_WEBHOOK_SECRET,_=e=>new Promise((t,r)=>{let s="";e.on("data",e=>{s+=e}),e.on("end",()=>{t(s)}),e.on("error",e=>{r(e)})});async function n(e,t){let r;if("POST"!==e.method)return t.status(405).json({error:"Method not allowed"});let s=e.headers["stripe-signature"];try{let t=await _(e);r=l.webhooks.constructEvent(t,s,p)}catch(e){return console.error("Webhook signature verification failed:",e.message),t.status(400).json({error:"Webhook signature verification failed"})}let o=(0,a.z)();try{switch(console.log(`Processing webhook event: ${r.type}`),r.type){case"checkout.session.completed":{let e=r.data.object;console.log("Checkout session completed:",{sessionId:e.id,mode:e.mode,userId:e.metadata?.user_id,customerId:e.customer}),"payment"===e.mode&&await d(o,e);break}case"payment_intent.succeeded":{let e=r.data.object;console.log("Payment succeeded:",e.id);break}case"payment_intent.payment_failed":{let e=r.data.object;console.log("Payment failed:",e.id),await c(o,e);break}case"customer.created":{let e=r.data.object;console.log("Customer created:",e.id);break}default:console.log(`Unhandled event type: ${r.type}`)}t.status(200).json({received:!0})}catch(e){console.error("Error processing webhook:",e),t.status(500).json({error:"Webhook processing failed"})}}async function d(e,t){let r=t.metadata.user_id,s=t.payment_intent,o=t.customer;if(!r)throw console.error("No user_id found in session metadata"),Error("Missing user_id in session metadata");if(!s)throw console.error("No payment_intent found in session"),Error("Missing payment_intent in session");let a=await l.checkout.sessions.listLineItems(t.id);if(!a.data||0===a.data.length)throw console.error("No line items found in session"),Error("No line items found in session");let i={price_1Rbz9CG3dq2cZKhWCnIVHUx6:"starter",price_1RbzCCG3dq2cZKhWS3MlXiCZ:"pro",price_1RbzDHG3dq2cZKhWID6C0aMY:"creator"}[a.data[0].price.id]||"starter",{credits:n,amount:d}=function(e){let t={starter:{credits:50,amount:10},pro:{credits:200,amount:30},creator:{credits:600,amount:75}};return t[e]||t.starter}(i);console.log(`Processing credit pack purchase: ${i} - ${n} credits for user ${r}`);try{let{data:t}=await e.from("credit_purchases").select("id").eq("stripe_payment_intent_id",s).single();if(t)return void console.log(`Payment ${s} already processed, skipping`);let{error:a}=await e.from("credit_purchases").insert({user_id:r,stripe_payment_intent_id:s,stripe_customer_id:o,status:"succeeded",tier:i,credits_purchased:n,amount_paid:d,expires_at:new Date(Date.now()+15552e6).toISOString()});if(a)throw console.error("Error inserting credit purchase:",a),a;let{data:c}=await e.from("user_credits").select("total_credits, used_credits").eq("user_id",r).single();if(c){let{error:t}=await e.from("user_credits").update({total_credits:c.total_credits+n,last_purchase_date:new Date().toISOString(),updated_at:new Date().toISOString()}).eq("user_id",r);if(t)throw console.error("Error updating user credits:",t),t;console.log(`Updated user ${r}: ${c.total_credits} + ${n} = ${c.total_credits+n} total credits`)}else{let{error:t}=await e.from("user_credits").insert({user_id:r,total_credits:n,used_credits:0,last_purchase_date:new Date().toISOString()});if(t)throw console.error("Error inserting user credits:",t),t;console.log(`Created new credit record for user ${r}: ${n} total credits`)}await e.from("usage_stats").upsert({user_id:r,credits_used_this_month:0,videos_extracted_this_month:0,last_reset_date:new Date().toISOString()},{onConflict:"user_id"}),console.log(`Successfully added ${n} credits to user ${r}`)}catch(e){throw console.error("Error processing credit pack purchase:",e),e}}async function c(e,t){console.log("Handling payment failure for:",t.id);let{error:r}=await e.from("credit_purchases").update({status:"failed",updated_at:new Date().toISOString()}).eq("stripe_payment_intent_id",t.id);r&&console.error("Error updating failed payment:",r)}s()}catch(e){s(e)}})},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6157:(e,t,r)=>{r.d(t,{z:()=>o});var s=r(3939);function o(){let e="https://vsaxiialrhpqdlcpmetn.supabase.co",t=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!e||!t)throw Error("Missing Supabase environment variables");return(0,s.createClient)(e,t,{auth:{autoRefreshToken:!1,persistSession:!1}})}},6435:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},8667:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},9767:e=>{e.exports=import("stripe")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=t(t.s=262);module.exports=r})();