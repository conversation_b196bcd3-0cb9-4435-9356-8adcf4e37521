"use strict";(()=>{var e={};e.id=639,e.ids=[639],e.modules={45:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>n});var a=r(9767),i=r(6157),o=e([a]);let u=new(a=(o.then?(await o)():o)[0]).default(process.env.STRIPE_SECRET_KEY);async function n(e,t){if(t.setHeader("Access-Control-Allow-Origin","*"),t.setHeader("Access-Control-Allow-Methods","POST, OPTIONS"),t.setHeader("Access-Control-Allow-Headers","Content-Type, Authorization"),"OPTIONS"===e.method)return t.status(200).end();if("POST"!==e.method)return t.status(405).json({error:"Method not allowed"});try{let{priceId:r,userId:s,userEmail:a}=e.body;if(!r||!s||!a)return t.status(400).json({error:"Missing required fields"});let o=(0,i.z)(),{data:n}=await o.from("users").select("stripe_customer_id").eq("id",s).single(),c=n?.stripe_customer_id;c||(c=(await u.customers.create({email:a,metadata:{supabase_user_id:s}})).id,await o.from("users").update({stripe_customer_id:c}).eq("id",s));let d=await u.checkout.sessions.create({customer:c,payment_method_types:["card"],line_items:[{price:r,quantity:1}],mode:"payment",success_url:`${e.headers.origin||"http://localhost:3000"}/dashboard?session_id={CHECKOUT_SESSION_ID}`,cancel_url:`${e.headers.origin||"http://localhost:3000"}/pricing`,metadata:{user_id:s}});t.status(200).json({sessionId:d.id})}catch(e){console.error("Error creating checkout session:",e),t.status(500).json({error:"Failed to create checkout session"})}}s()}catch(e){s(e)}})},410:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{config:()=>d,default:()=>c,routeModule:()=>l});var a=r(3480),i=r(8667),o=r(6435),n=r(45),u=e([n]);n=(u.then?(await u)():u)[0];let c=(0,o.M)(n,"default"),d=(0,o.M)(n,"config"),l=new a.PagesAPIRouteModule({definition:{kind:i.A.PAGES_API,page:"/api/stripe/checkout",pathname:"/api/stripe/checkout",bundlePath:"",filename:""},userland:n});s()}catch(e){s(e)}})},3480:(e,t,r)=>{e.exports=r(5600)},3939:e=>{e.exports=require("@supabase/supabase-js")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6157:(e,t,r)=>{r.d(t,{z:()=>a});var s=r(3939);function a(){let e="https://vsaxiialrhpqdlcpmetn.supabase.co",t=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!e||!t)throw Error("Missing Supabase environment variables");return(0,s.createClient)(e,t,{auth:{autoRefreshToken:!1,persistSession:!1}})}},6435:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},8667:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},9767:e=>{e.exports=import("stripe")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=t(t.s=410);module.exports=r})();