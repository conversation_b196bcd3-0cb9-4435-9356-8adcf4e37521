"use strict";(()=>{var e={};e.id=431,e.ids=[431],e.modules={3480:(e,t,n)=>{e.exports=n(5600)},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6180:(e,t,n)=>{n.r(t),n.d(t,{config:()=>c,default:()=>d,routeModule:()=>h});var r={};n.r(r),n.d(r,{default:()=>l});var a=n(3480),o=n(8667),i=n(6435),s=n(7954);let u=e=>({en:"English",es:"Spanish",fr:"French",de:"German",it:"Italian",pt:"Portuguese",ru:"Russian",ja:"Japanese",ko:"Korean",zh:"Chinese",ar:"Arabic",hi:"Hindi",tr:"Turkish",pl:"Polish",nl:"Dutch",sv:"Swedish",da:"Danish",no:"Norwegian",fi:"Finnish",cs:"Czech",hu:"Hungarian",ro:"Romanian",bg:"Bulgarian",hr:"Croatian",sk:"Slovak",sl:"Slovenian",et:"Estonian",lv:"Latvian",lt:"Lithuanian",uk:"Ukrainian",el:"Greek",he:"Hebrew",th:"Thai",vi:"Vietnamese",id:"Indonesian",ms:"Malay",tl:"Filipino",sw:"Swahili",af:"Afrikaans"})[e]||e.toUpperCase(),l=async(e,t)=>{if(t.setHeader("Access-Control-Allow-Origin","*"),t.setHeader("Access-Control-Allow-Methods","GET, POST, PUT, DELETE, OPTIONS"),t.setHeader("Access-Control-Allow-Headers","Content-Type, Authorization"),"OPTIONS"===e.method)return void t.status(200).end();if("GET"!==e.method)return void t.status(405).json({error:"Method not allowed"});try{let n=e.headers.authorization;if(n)try{let r=await fetch(`${e.headers.host?.includes("localhost")?"http":"https"}://${e.headers.host}/api/user/consume-credits`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:n},body:JSON.stringify({credits:1})});if(!r.ok){let e=await r.json();return t.status(r.status).json(e)}}catch(e){return console.error("Error consuming credits:",e),t.status(500).json({error:"Failed to process credit consumption"})}let{videoId:r}=e.query;if(!r)return t.status(400).json({error:"Video ID is required"});console.log(`Fetching subtitle languages for video: ${r}`);let a=`https://www.youtube.com/watch?v=${r}`,o={dumpSingleJson:!0,noCheckCertificates:!0,noWarnings:!0,preferFreeFormats:!0,addHeader:["referer:youtube.com","user-agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"]};process.env.SOCKET_URL&&"true"===process.env.SOCKET_ENABLED&&(o.proxy=process.env.SOCKET_URL);let i=await s(a,o),l=[];if(i.subtitles&&Object.keys(i.subtitles).forEach(e=>{let t=u(e);l.push({code:e,name:t,isAutoGenerated:!1})}),i.automatic_captions&&Object.keys(i.automatic_captions).forEach(e=>{if(!l.find(t=>t.code===e)){let t=u(e);l.push({code:e,name:t,isAutoGenerated:!0})}}),0===l.length)return t.status(404).json({error:"No subtitles available for this video"});l.sort((e,t)=>"en"===e.code?-1:"en"===t.code?1:e.name.localeCompare(t.name)),t.status(200).json({videoId:r,title:i.title||"YouTube Video",channel:i.uploader||i.channel||"Unknown Channel",channelId:i.uploader_id||i.channel_id||null,channelUrl:i.uploader_url||i.channel_url||null,thumbnail:i.thumbnail||`https://img.youtube.com/vi/${r}/hqdefault.jpg`,duration:i.duration||null,uploadDate:i.upload_date||null,viewCount:i.view_count||null,languages:l})}catch(e){console.error("Error fetching subtitle languages:",e),t.status(500).json({error:"Failed to fetch subtitle languages",details:e instanceof Error?e.message:"Unknown error"})}},d=(0,i.M)(r,"default"),c=(0,i.M)(r,"config"),h=new a.PagesAPIRouteModule({definition:{kind:o.A.PAGES_API,page:"/api/subtitles/languages/[videoId]",pathname:"/api/subtitles/languages/[videoId]",bundlePath:"",filename:""},userland:r})},6435:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,n){return n in t?t[n]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,n)):"function"==typeof t&&"default"===n?t:void 0}}})},7954:e=>{e.exports=require("youtube-dl-exec")},8667:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return n}});var n=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var n=t(t.s=6180);module.exports=n})();