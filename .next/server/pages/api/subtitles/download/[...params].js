"use strict";(()=>{var t={};t.id=958,t.ids=[958],t.modules={3480:(t,e,r)=>{t.exports=r(5600)},5600:t=>{t.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6435:(t,e)=>{Object.defineProperty(e,"M",{enumerable:!0,get:function(){return function t(e,r){return r in e?e[r]:"then"in e&&"function"==typeof e.then?e.then(e=>t(e,r)):"function"==typeof e&&"default"===r?e:void 0}}})},7259:(t,e,r)=>{r.r(e),r.d(e,{config:()=>x,default:()=>b,routeModule:()=>w});var n={};r.r(n),r.d(n,{default:()=>p});var o=r(3480),a=r(8667),l=r(6435),s=r(7954);let i=async t=>{try{let e=await fetch(t),r=await e.text();if(r.includes("WEBVTT"))return u(r);try{let t=JSON.parse(r);if(t.events)return d(t)}catch(t){}return u(r)}catch(t){return console.error("Error downloading/parsing subtitles:",t),[]}},u=t=>{let e=[],r=t.split("\n");for(let t=0;t<r.length;t++){let n=r[t].trim();if(n.includes("--\x3e")){let o=n.match(/^([\d:.,]+)\s*-->\s*([\d:.,]+)/);if(!o)continue;let a=o[1],l=o[2],s=c(a),i=c(l),u=[],d=[];for(t++;t<r.length&&!r[t].includes("--\x3e");){let e=r[t].trim();if(""===e)break;if(e)if(e.includes("<c>")||e.match(/<\d+:\d+:\d+\.\d+>/))d.push(e);else{let t=e.replace(/<[^>]*>/g,"").replace(/\s+/g," ").trim();t&&t.length>0&&u.push(t)}t++}t--,0===u.length&&d.length>0&&d.forEach(t=>{let e=t.replace(/<\d+:\d+:\d+\.\d+>/g,"").replace(/<[^>]*>/g,"").replace(/\s+/g," ").trim();e&&e.length>0&&u.push(e)});let p=i-s,h=u.join(" ").trim();h&&h.length>0&&p>.1&&p<30&&e.push({start:s,end:i,text:h})}}let n=[];for(let t=0;t<e.length;t++){let r=e[t],o=e[t+1];o&&o.start-r.end<.2&&r.text&&o.text&&r.text!==o.text&&!o.text.includes(r.text)?(n.push({start:r.start,end:o.end,text:`${r.text} ${o.text}`.trim()}),t++):n.push(r)}return n},d=t=>{let e=[];if(t.events){for(let r of t.events)if(r.segs){let t="";for(let e of r.segs)e.utf8&&(t+=e.utf8);t.trim()&&e.push({start:r.tStartMs/1e3,end:(r.tStartMs+r.dDurationMs)/1e3,text:t.trim()})}}return e},c=t=>{let e=t.replace(",",".").split(":");if(3===e.length){let t=parseInt(e[0])||0;return 3600*t+60*(parseInt(e[1])||0)+(parseFloat(e[2])||0)}return 2===e.length?60*(parseInt(e[0])||0)+(parseFloat(e[1])||0):0},p=async(t,e)=>{if(e.setHeader("Access-Control-Allow-Origin","*"),e.setHeader("Access-Control-Allow-Methods","GET, POST, PUT, DELETE, OPTIONS"),e.setHeader("Access-Control-Allow-Headers","Content-Type, Authorization"),"OPTIONS"===t.method)return void e.status(200).end();if("GET"!==t.method)return void e.status(405).json({error:"Method not allowed"});try{let r=t.headers.authorization;if(r)try{let n=await fetch(`${t.headers.host?.includes("localhost")?"http":"https"}://${t.headers.host}/api/user/consume-credits`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:r},body:JSON.stringify({credits:1})});if(!n.ok){let t=await n.json();return e.status(n.status).json(t)}}catch(t){return console.error("Error consuming credits:",t),e.status(500).json({error:"Failed to process credit consumption"})}let{params:n}=t.query;console.log("Request URL:",t.url),console.log("Query params:",t.query),console.log("Params:",n);let o=n?.[0]?.split("-")||[];if(o.length<2)return e.status(400).json({error:"Invalid parameters. Expected format: videoId-langCode"});let[a,l]=o;if(!a)return e.status(400).json({error:"Video ID is required"});if(!l)return e.status(400).json({error:"Language code is required"});console.log(`Processing request for video: ${a}, language: ${l}`);let u=`https://www.youtube.com/watch?v=${a}`,d={dumpSingleJson:!0,noCheckCertificates:!0,noWarnings:!0,preferFreeFormats:!0,addHeader:["referer:youtube.com","user-agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"]};process.env.SOCKET_URL&&"true"===process.env.SOCKET_ENABLED&&(d.proxy=process.env.SOCKET_URL);let c=await s(u,d),p=[],$=null;if(c.subtitles&&c.subtitles[l]){let t=c.subtitles[l],r=t.find(t=>"vtt"===t.ext)||t[0];if(!r)return e.status(404).json({error:"Subtitles not found for the specified language"});$=r.url,console.log(`Found subtitles for ${l}: ${r.ext} format`)}else{if(!c.automatic_captions||!c.automatic_captions[l])return e.status(404).json({error:"No subtitles available for this video"});let t=c.automatic_captions[l],r=t.find(t=>"vtt"===t.ext)||t[0];if(!r)return e.status(404).json({error:"Subtitles not found for the specified language"});$=r.url,console.log(`Found auto-generated subtitles for ${l}: ${r.ext} format`)}$&&(console.log(`Downloading subtitles from ${$}`),p=await i($)),e.status(200).json({videoId:a,title:c.title||"YouTube Video",channel:c.uploader||c.channel||"Unknown Channel",channelId:c.uploader_id||c.channel_id||null,channelUrl:c.uploader_url||c.channel_url||null,thumbnail:c.thumbnail||`https://img.youtube.com/vi/${a}/hqdefault.jpg`,duration:c.duration||null,uploadDate:c.upload_date||null,viewCount:c.view_count||null,language:l,subtitles:p,formats:{vtt:h(p,c,l),srt:f(p),txt:g(p,c,l),json:m(p,c,l)}})}catch(t){console.error("Error downloading subtitles:",t),e.status(500).json({error:"Failed to download subtitles",details:t instanceof Error?t.message:"Unknown error"})}},h=(t,e,r)=>{let n="WEBVTT\n";return n+=`Kind: captions
Language: ${r}

`,e.title&&(n+=`NOTE
Title: ${e.title}
`,(e.uploader||e.channel)&&(n+=`Channel: ${e.uploader||e.channel}
`),n+=`Video ID: ${e.id||"Unknown"}

`),t.forEach(t=>{let e=$(t.start),r=$(t.end);n+=`${e} --> ${r}
${t.text}

`}),n},f=t=>{let e="";return t.forEach((t,r)=>{let n=S(t.start),o=S(t.end);e+=`${r+1}
${n} --> ${o}
${t.text}

`}),e},g=(t,e,r)=>{let n="";return e.title&&(n+=`Title: ${e.title}
`),(e.uploader||e.channel)&&(n+=`Channel: ${e.uploader||e.channel}
`),n+=`Video ID: ${e.id||"Unknown"}
Language: ${r}
`,e.duration&&(n+=`Duration: ${Math.floor(e.duration/60)}:${String(e.duration%60).padStart(2,"0")}
`),n+=`
--- Subtitles ---

`,t.forEach(t=>{let e=Math.floor(t.start/60),r=Math.floor(t.start%60),o=Math.floor(t.end/60),a=Math.floor(t.end%60);n+=`[${e}:${String(r).padStart(2,"0")} - ${o}:${String(a).padStart(2,"0")}] ${t.text}
`}),n},m=(t,e,r)=>JSON.stringify({metadata:{title:e.title||"YouTube Video",channel:e.uploader||e.channel||"Unknown Channel",channelId:e.uploader_id||e.channel_id||null,channelUrl:e.uploader_url||e.channel_url||null,videoId:e.id||"Unknown",language:r,duration:e.duration||null,uploadDate:e.upload_date||null,viewCount:e.view_count||null,thumbnail:e.thumbnail||null,extractedAt:new Date().toISOString(),totalSubtitles:t.length},subtitles:t.map((t,e)=>({index:e+1,start:t.start,end:t.end,duration:t.end-t.start,text:t.text,startTime:$(t.start),endTime:$(t.end)}))},null,2),$=t=>{let e=Math.floor(t/3600),r=Math.floor(t%3600/60);return`${String(e).padStart(2,"0")}:${String(r).padStart(2,"0")}:${(t%60).toFixed(3).padStart(6,"0")}`},S=t=>{let e=Math.floor(t/3600),r=Math.floor(t%3600/60);return`${String(e).padStart(2,"0")}:${String(r).padStart(2,"0")}:${(t%60).toFixed(3).replace(".",",").padStart(6,"0")}`},b=(0,l.M)(n,"default"),x=(0,l.M)(n,"config"),w=new o.PagesAPIRouteModule({definition:{kind:a.A.PAGES_API,page:"/api/subtitles/download/[...params]",pathname:"/api/subtitles/download/[...params]",bundlePath:"",filename:""},userland:n})},7954:t=>{t.exports=require("youtube-dl-exec")},8667:(t,e)=>{Object.defineProperty(e,"A",{enumerable:!0,get:function(){return r}});var r=function(t){return t.PAGES="PAGES",t.PAGES_API="PAGES_API",t.APP_PAGE="APP_PAGE",t.APP_ROUTE="APP_ROUTE",t.IMAGE="IMAGE",t}({})}};var e=require("../../../../webpack-api-runtime.js");e.C(t);var r=e(e.s=7259);module.exports=r})();