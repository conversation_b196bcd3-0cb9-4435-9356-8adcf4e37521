"use strict";(()=>{var e={};e.id=826,e.ids=[826],e.modules={3480:(e,t,r)=>{e.exports=r(5600)},3939:e=>{e.exports=require("@supabase/supabase-js")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6435:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},6955:(e,t,r)=>{r.r(t),r.d(t,{config:()=>d,default:()=>u,routeModule:()=>c});var a={};r.r(a),r.d(a,{default:()=>l});var s=r(3480),o=r(8667),i=r(6435),n=r(9447);async function l(e,t){if(t.setHeader("Access-Control-Allow-Origin","*"),t.setHeader("Access-Control-Allow-Methods","GET, OPTIONS"),t.setHeader("Access-Control-Allow-Headers","Content-Type, Authorization"),"OPTIONS"===e.method)return t.status(200).end();if("GET"!==e.method)return t.status(405).json({error:"Method not allowed"});try{let{videoId:r}=e.query;if(!r)return t.status(400).json({error:"Video ID is required"});let a=e.headers.authorization;if(!a)return t.status(401).json({error:"Authorization required"});let s=(0,n.z)(),{data:{user:o},error:i}=await s.auth.getUser(a.replace("Bearer ",""));if(i||!o)return t.status(401).json({error:"Invalid authorization"});let{data:l,error:u}=await s.from("user_credits").select("available_credits").eq("user_id",o.id).single();if(u||!l||l.available_credits<1)return t.status(403).json({error:"Insufficient credits. You need 1 credit to download all languages."});let d=await fetch(`${e.headers.origin||"http://localhost:3001"}/api/subtitles/languages/${r}`,{headers:{Authorization:a}});if(!d.ok)return t.status(400).json({error:"Failed to get available languages"});let c=await d.json(),p=c.languages||[];if(0===p.length)return t.status(404).json({error:"No languages available for this video"});let h={},g=p.map(async t=>{try{let s=await fetch(`${e.headers.origin||"http://localhost:3001"}/api/subtitles/download/${r}-${t.code}`,{headers:{Authorization:a}});if(s.ok){let e=await s.json();h[t.code]={language:t.name,subtitles:e.subtitles||[],isAutoGenerated:t.isAutoGenerated}}}catch(e){console.error(`Failed to download ${t.code}:`,e)}});await Promise.all(g);let{error:A}=await s.rpc("consume_credits",{p_user_id:o.id,p_credits:1});if(A)return console.error("Error consuming credits:",A),t.status(500).json({error:"Failed to process credit consumption"});let f={videoTitle:c.title||"Unknown",videoId:r,downloadedAt:new Date().toISOString(),languages:h,totalLanguages:Object.keys(h).length};t.status(200).json({success:!0,zipData:JSON.stringify(f,null,2),totalLanguages:Object.keys(h).length,message:`Downloaded ${Object.keys(h).length} languages`})}catch(e){console.error("Error downloading all languages:",e),t.status(500).json({error:"Internal server error"})}}let u=(0,i.M)(a,"default"),d=(0,i.M)(a,"config"),c=new s.PagesAPIRouteModule({definition:{kind:o.A.PAGES_API,page:"/api/subtitles/download-all/[videoId]",pathname:"/api/subtitles/download-all/[videoId]",bundlePath:"",filename:""},userland:a})},8667:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},9447:(e,t,r)=>{r.d(t,{z:()=>i});var a=r(3939);let s="https://vsaxiialrhpqdlcpmetn.supabase.co",o="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZzYXhpaWFscmhwcWRsY3BtZXRuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwOTQ1NzAsImV4cCI6MjA2NTY3MDU3MH0.MDe8-SLF1-73hqcunFsgOEvDYek1ku7Ap_A1UNardA8";s&&o&&s.includes("placeholder"),(0,a.createClient)(s,o,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"pkce"}});let i=()=>{let e=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!e)throw Error("Missing SUPABASE_SERVICE_ROLE_KEY environment variable");return(0,a.createClient)("https://vsaxiialrhpqdlcpmetn.supabase.co",e,{auth:{autoRefreshToken:!1,persistSession:!1}})}}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var r=t(t.s=6955);module.exports=r})();