"use strict";(()=>{var e={};e.id=758,e.ids=[758],e.modules={3480:(e,t,r)=>{e.exports=r(5600)},3716:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{config:()=>d,default:()=>c,routeModule:()=>l});var i=r(3480),n=r(8667),o=r(6435),a=r(4520),u=e([a]);a=(u.then?(await u)():u)[0];let c=(0,o.M)(a,"default"),d=(0,o.M)(a,"config"),l=new i.PagesAPIRouteModule({definition:{kind:n.A.PAGES_API,page:"/api/user/subscription",pathname:"/api/user/subscription",bundlePath:"",filename:""},userland:a});s()}catch(e){s(e)}})},3939:e=>{e.exports=require("@supabase/supabase-js")},4520:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>a});var i=r(9767),n=r(9447),o=e([i]);let u=new(i=(o.then?(await o)():o)[0]).default(process.env.STRIPE_SECRET_KEY);async function a(e,t){if(t.setHeader("Access-Control-Allow-Origin","*"),t.setHeader("Access-Control-Allow-Methods","GET, DELETE, OPTIONS"),t.setHeader("Access-Control-Allow-Headers","Content-Type, Authorization"),"OPTIONS"===e.method)return t.status(200).end();let r=(0,n.z)();try{if("GET"===e.method){let{userId:s}=e.query;if(!s)return t.status(400).json({error:"User ID required"});let{data:i,error:n}=await r.from("subscriptions").select("*").eq("user_id",s).eq("status","active").single();if(n&&"PGRST116"!==n.code)throw n;let{data:o,error:a}=await r.from("usage_stats").select("*").eq("user_id",s).single();if(a&&"PGRST116"!==a.code)throw a;t.status(200).json({subscription:i,usage:o})}else if("DELETE"===e.method){let{subscriptionId:s}=e.body;if(!s)return t.status(400).json({error:"Subscription ID required"});let i=await u.subscriptions.update(s,{cancel_at_period_end:!0}),{error:n}=await r.from("subscriptions").update({cancel_at_period_end:!0,updated_at:new Date().toISOString()}).eq("stripe_subscription_id",s);if(n)throw n;t.status(200).json({success:!0,subscription:i})}else t.status(405).json({error:"Method not allowed"})}catch(e){console.error("Error in subscription endpoint:",e),t.status(500).json({error:"Internal server error"})}}s()}catch(e){s(e)}})},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6435:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},8667:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},9447:(e,t,r)=>{r.d(t,{z:()=>o});var s=r(3939);let i="https://vsaxiialrhpqdlcpmetn.supabase.co",n="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZzYXhpaWFscmhwcWRsY3BtZXRuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwOTQ1NzAsImV4cCI6MjA2NTY3MDU3MH0.MDe8-SLF1-73hqcunFsgOEvDYek1ku7Ap_A1UNardA8";i&&n&&i.includes("placeholder"),(0,s.createClient)(i,n,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"pkce"}});let o=()=>{let e=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!e)throw Error("Missing SUPABASE_SERVICE_ROLE_KEY environment variable");return(0,s.createClient)("https://vsaxiialrhpqdlcpmetn.supabase.co",e,{auth:{autoRefreshToken:!1,persistSession:!1}})}},9767:e=>{e.exports=import("stripe")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=t(t.s=3716);module.exports=r})();