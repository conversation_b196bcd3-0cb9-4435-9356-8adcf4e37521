"use strict";(()=>{var e={};e.id=765,e.ids=[765],e.modules={3480:(e,r,t)=>{e.exports=t(5600)},3939:e=>{e.exports=require("@supabase/supabase-js")},4890:(e,r,t)=>{t.r(r),t.d(r,{config:()=>l,default:()=>d,routeModule:()=>c});var s={};t.r(s),t.d(s,{default:()=>u});var n=t(3480),a=t(8667),o=t(6435),i=t(6157);async function u(e,r){if(r.setHeader("Access-Control-Allow-Origin","*"),r.setHeader("Access-Control-Allow-Methods","GET, OPTIONS"),r.setHeader("Access-Control-Allow-Headers","Content-Type, Authorization"),"OPTIONS"===e.method)return r.status(200).end();if("GET"!==e.method)return r.status(405).json({error:"Method not allowed"});try{let t=e.headers.authorization;if(!t)return r.status(401).json({error:"Authorization required"});let s=(0,i.z)(),{data:{user:n},error:a}=await s.auth.getUser(t.replace("Bearer ",""));if(a||!n)return r.status(401).json({error:"Invalid authorization"});let{data:o,error:u}=await s.from("user_credits").select("*").eq("user_id",n.id).single(),{data:d,error:l}=await s.from("usage_stats").select("*").eq("user_id",n.id).single();r.status(200).json({userCredits:o||null,usage:d||null,creditsError:u?.code==="PGRST116"?null:u,usageError:l?.code==="PGRST116"?null:l})}catch(e){console.error("Error fetching credit data:",e),r.status(500).json({error:"Internal server error"})}}let d=(0,o.M)(s,"default"),l=(0,o.M)(s,"config"),c=new n.PagesAPIRouteModule({definition:{kind:a.A.PAGES_API,page:"/api/user/credits",pathname:"/api/user/credits",bundlePath:"",filename:""},userland:s})},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6157:(e,r,t)=>{t.d(r,{z:()=>n});var s=t(3939);function n(){let e="https://vsaxiialrhpqdlcpmetn.supabase.co",r=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!e||!r)throw Error("Missing Supabase environment variables");return(0,s.createClient)(e,r,{auth:{autoRefreshToken:!1,persistSession:!1}})}},6435:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},8667:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})}};var r=require("../../../webpack-api-runtime.js");r.C(e);var t=r(r.s=4890);module.exports=t})();