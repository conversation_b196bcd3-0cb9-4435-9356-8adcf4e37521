"use strict";(()=>{var e={};e.id=240,e.ids=[240],e.modules={2143:(e,r,t)=>{t.r(r),t.d(r,{config:()=>c,default:()=>d,routeModule:()=>l});var s={};t.r(s),t.d(s,{default:()=>u});var n=t(3480),i=t(8667),o=t(6435),a=t(6157);async function u(e,r){if(r.setHeader("Access-Control-Allow-Origin","*"),r.setHeader("Access-Control-Allow-Methods","POST, OPTIONS"),r.setHeader("Access-Control-Allow-Headers","Content-Type, Authorization"),"OPTIONS"===e.method)return r.status(200).end();if("POST"!==e.method)return r.status(405).json({error:"Method not allowed"});try{let{credits:t=1}=e.body,s=e.headers.authorization;if(!s)return r.status(401).json({error:"Authorization required"});let n=(0,a.z)(),{data:{user:i},error:o}=await n.auth.getUser(s.replace("Bearer ",""));if(o||!i)return r.status(401).json({error:"Invalid authorization"});let{data:u,error:d}=await n.from("user_credits").select("available_credits").eq("user_id",i.id).single();if(d||!u||u.available_credits<t)return r.status(403).json({error:`Insufficient credits. You need ${t} credit(s).`,availableCredits:u?.available_credits||0});let{data:c,error:l}=await n.rpc("consume_credits",{p_user_id:i.id,p_credits:t});if(l)return console.error("Error consuming credits:",l),r.status(500).json({error:"Failed to consume credits"});if(!c)return r.status(403).json({error:"Insufficient credits"});r.status(200).json({success:!0,creditsConsumed:t,message:`${t} credit(s) consumed successfully`})}catch(e){console.error("Error consuming credits:",e),r.status(500).json({error:"Internal server error"})}}let d=(0,o.M)(s,"default"),c=(0,o.M)(s,"config"),l=new n.PagesAPIRouteModule({definition:{kind:i.A.PAGES_API,page:"/api/user/consume-credits",pathname:"/api/user/consume-credits",bundlePath:"",filename:""},userland:s})},3480:(e,r,t)=>{e.exports=t(5600)},3939:e=>{e.exports=require("@supabase/supabase-js")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6157:(e,r,t)=>{t.d(r,{z:()=>n});var s=t(3939);function n(){let e="https://vsaxiialrhpqdlcpmetn.supabase.co",r=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!e||!r)throw Error("Missing Supabase environment variables");return(0,s.createClient)(e,r,{auth:{autoRefreshToken:!1,persistSession:!1}})}},6435:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},8667:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})}};var r=require("../../../webpack-api-runtime.js");r.C(e);var t=r(r.s=2143);module.exports=t})();