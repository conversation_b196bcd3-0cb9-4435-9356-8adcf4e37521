{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [{"source": "/api/:path*", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}], "regex": "^/api(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}], "dynamicRoutes": [{"page": "/api/subtitles/download/[...params]", "regex": "^/api/subtitles/download/(.+?)(?:/)?$", "routeKeys": {"nxtPparams": "nxtPparams"}, "namedRegex": "^/api/subtitles/download/(?<nxtPparams>.+?)(?:/)?$"}, {"page": "/api/subtitles/download-all/[videoId]", "regex": "^/api/subtitles/download\\-all/([^/]+?)(?:/)?$", "routeKeys": {"nxtPvideoId": "nxtPvideoId"}, "namedRegex": "^/api/subtitles/download\\-all/(?<nxtPvideoId>[^/]+?)(?:/)?$"}, {"page": "/api/subtitles/languages/[videoId]", "regex": "^/api/subtitles/languages/([^/]+?)(?:/)?$", "routeKeys": {"nxtPvideoId": "nxtPvideoId"}, "namedRegex": "^/api/subtitles/languages/(?<nxtPvideoId>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/auth/callback", "regex": "^/auth/callback(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/callback(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/disclaimer", "regex": "^/disclaimer(?:/)?$", "routeKeys": {}, "namedRegex": "^/disclaimer(?:/)?$"}, {"page": "/extractor", "regex": "^/extractor(?:/)?$", "routeKeys": {}, "namedRegex": "^/extractor(?:/)?$"}, {"page": "/faq", "regex": "^/faq(?:/)?$", "routeKeys": {}, "namedRegex": "^/faq(?:/)?$"}, {"page": "/pricing", "regex": "^/pricing(?:/)?$", "routeKeys": {}, "namedRegex": "^/pricing(?:/)?$"}, {"page": "/privacy", "regex": "^/privacy(?:/)?$", "routeKeys": {}, "namedRegex": "^/privacy(?:/)?$"}, {"page": "/terms", "regex": "^/terms(?:/)?$", "routeKeys": {}, "namedRegex": "^/terms(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": [{"source": "/health", "destination": "/api/health", "regex": "^/health(?:/)?$"}]}