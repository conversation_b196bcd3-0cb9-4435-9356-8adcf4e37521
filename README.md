# YouTube Subtitle Extractor

A Next.js application for extracting subtitles from YouTube videos with credit-based pricing and Google authentication.

## Features

- Extract subtitles from YouTube videos in multiple languages
- Support for VTT, TXT, and JSON formats
- Google OAuth authentication via Supabase
- Credit-based subscription system with Stripe
- Real-time processing with progress tracking
- Responsive design with Tailwind CSS

## Tech Stack

- **Frontend**: Next.js, React, TypeScript, Tailwind CSS
- **Backend**: Vercel Serverless Functions, youtube-dl-exec
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth with Google OAuth
- **Payments**: Stripe

## Getting Started

### Prerequisites

- Node.js 18+
- Supabase account
- Stripe account
- Google Cloud Console project (for OAuth)

### Installation

1. Clone and install:
```bash
git clone <repository-url>
cd YTSubtitleExtractor
npm install
```

2. Set up environment variables:
```bash
cp .env.example .env
```

3. Configure Supabase database:
- Run SQL from `database-setup.sql`
- Set up Google OAuth

4. Configure Stripe:
- Create products and pricing
- Set up webhook endpoint

5. Start development:
```bash
npm run dev
```

## Scripts

- `npm run dev` - Development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## Usage

1. Sign up with Google OAuth
2. Purchase credits via Stripe
3. Extract subtitles from YouTube videos
4. Download in VTT, TXT, or JSON format

## Deployment

Deploy to Vercel:

```bash
npm install -g vercel
vercel --prod
```

Set environment variables in Vercel dashboard for Supabase and Stripe configuration.

## Project Structure

```
├── components/          # React components
├── pages/              # Next.js pages and API routes
├── lib/                # Utility functions
├── styles/             # CSS styles
├── public/             # Static assets
├── scripts/            # Database scripts
└── database-setup.sql  # Supabase schema
```

## License

MIT License
